# PDF压缩功能文档

## 📋 项目概述

本文档记录PDF压缩功能的后端API参数、微信小程序配置和相关技术规范。

## 🔧 后端API配置 - Stirling-PDF

### 基本信息
- **项目地址**: https://github.com/Stirling-Tools/Stirling-PDF
- **API端点**: `/compress-pdf`
- **请求方法**: POST
- **内容类型**: multipart/form-data

### 输入参数

#### 必需参数
```javascript
{
  fileInput: File,           // PDF文件（必需）
}
```

#### 可选参数
```javascript
{
  optimizeLevel: Number,     // 压缩级别 1-9
  enableGrayscale: Boolean,  // 是否启用灰度压缩
  expectedOutputSize: String // 期望输出大小，如 "25MB", "10.8MB"
}
```

### 压缩级别说明
- **1-3**: PDF压缩（保持高质量，适合文档类PDF）
- **4-6**: 轻度图像压缩（平衡压缩，适合包含图像的文档）
- **7-9**: 强度图像压缩（最大压缩，显著降低图像质量）

### 服务器配置

#### Docker部署示例
```bash
docker run -d \
  --name stirling-pdf \
  -p 8080:8080 \
  -e SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE=200MB \
  -e SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE=200MB \
  -e JAVA_OPTS="-Xmx4g -Xms2g" \
  frooodle/s-pdf:latest
```

#### 环境变量配置
```bash
# 文件大小限制
SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE=200MB
SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE=200MB

# API认证（可选）
SECURITY_CUSTOMGLOBALAPIKEY=your-custom-api-key

# JVM内存配置
JAVA_OPTS="-Xmx4g -Xms2g"
```

### API调用示例

#### cURL示例
```bash
curl -X POST "http://your-server:8080/compress-pdf" \
  -H "Content-Type: multipart/form-data" \
  -H "X-API-KEY: your-api-key" \
  -F "fileInput=@/path/to/file.pdf" \
  -F "optimizeLevel=5" \
  -F "enableGrayscale=false" \
  -F "expectedOutputSize=10MB" \
  --output compressed_output.pdf
```

#### JavaScript示例
```javascript
const formData = new FormData();
formData.append('fileInput', pdfFile);
formData.append('optimizeLevel', '5');
formData.append('enableGrayscale', 'false');
formData.append('expectedOutputSize', '10MB');

fetch('http://your-server:8080/compress-pdf', {
  method: 'POST',
  headers: {
    'X-API-KEY': 'your-api-key'
  },
  body: formData
})
.then(response => response.blob())
.then(blob => {
  // 处理压缩后的PDF
});
```

## 📱 微信小程序配置

### 文件上传限制
- **单文件最大大小**: 10MB
- **API接口**: `wx.uploadFile`
- **超过限制**: 需要使用分块上传

### 小程序配置

#### app.json 网络配置
```json
{
  "networkTimeout": {
    "request": 60000,
    "uploadFile": 60000,
    "downloadFile": 60000
  }
}
```

#### 服务器域名配置
需要在微信公众平台配置以下域名：
- **request合法域名**: `https://your-stirling-pdf-server.com`
- **uploadFile合法域名**: `https://your-stirling-pdf-server.com`
- **downloadFile合法域名**: `https://your-stirling-pdf-server.com`

### 文件大小检查和处理策略
```javascript
const checkAndHandleFileSize = (filePath) => {
  return new Promise((resolve, reject) => {
    wx.getFileInfo({
      filePath: filePath,
      success: (res) => {
        const sizeMB = res.size / (1024 * 1024);

        if (sizeMB <= 10) {
          // 小文件：直接使用 wx.uploadFile
          console.log('使用标准上传方式');
          resolve({ type: 'standard', size: sizeMB });

        } else if (sizeMB <= 100) {
          // 中等文件：使用分块上传
          console.log('使用分块上传方式');
          resolve({ type: 'chunk', size: sizeMB });

        } else {
          // 超大文件：无法处理
          wx.showModal({
            title: '文件过大',
            content: `文件大小为${sizeMB.toFixed(1)}MB，超过100MB限制。\n\n建议：\n1. 使用其他工具先压缩PDF\n2. 分割成多个小文件\n3. 选择较小的PDF文件`,
            showCancel: false
          });
          reject(new Error(`文件超过100MB限制，微信小程序无法处理`));
        }
      },
      fail: reject
    });
  });
};
```

### 分块上传实现（10MB-100MB文件）
```javascript
class PDFChunkUploader {
  constructor(filePath, uploadUrl, options = {}) {
    this.filePath = filePath;
    this.uploadUrl = uploadUrl;
    this.chunkSize = options.chunkSize || 5 * 1024 * 1024; // 5MB
    this.fs = wx.getFileSystemManager();
    this.options = options;
  }

  async uploadLargePDF() {
    try {
      // 1. 获取文件信息
      const fileInfo = await this.getFileInfo();
      const totalChunks = Math.ceil(fileInfo.size / this.chunkSize);

      // 2. 逐块上传
      for (let i = 0; i < totalChunks; i++) {
        await this.uploadChunk(i, fileInfo.size, totalChunks);
      }

      // 3. 通知服务器合并并压缩
      return await this.mergeAndCompress();

    } catch (error) {
      console.error('分块上传失败:', error);
      throw error;
    }
  }

  uploadChunk(index, totalSize, totalChunks) {
    return new Promise((resolve, reject) => {
      const start = index * this.chunkSize;
      const length = Math.min(this.chunkSize, totalSize - start);

      this.fs.readFile({
        filePath: this.filePath,
        position: start,
        length: length,
        success: (res) => {
          wx.request({
            url: `${this.uploadUrl}/chunk`,
            method: 'POST',
            data: res.data,
            header: {
              'content-type': 'application/octet-stream',
              'chunk-index': index,
              'total-chunks': totalChunks,
              'chunk-size': length,
              'X-API-KEY': 'your-api-key'
            },
            success: resolve,
            fail: reject
          });
        },
        fail: reject
      });
    });
  }
}

// 标准上传实现（10MB以下文件）
const uploadSmallPDF = (filePath, options = {}) => {
  const {
    optimizeLevel = 5,
    enableGrayscale = false,
    expectedOutputSize = ''
  } = options;

  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: 'https://your-server.com/compress-pdf',
      filePath: filePath,
      name: 'fileInput',
      formData: {
        optimizeLevel: optimizeLevel.toString(),
        enableGrayscale: enableGrayscale.toString(),
        expectedOutputSize: expectedOutputSize
      },
      header: {
        'X-API-KEY': 'your-api-key'
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res);
        } else {
          reject(new Error(`上传失败: ${res.statusCode}`));
        }
      },
      fail: reject
    });
  });
};
```

## 🔒 安全配置

### API认证
```javascript
// 请求头中添加API密钥
headers: {
  'X-API-KEY': 'your-api-key'
}
```

### HTTPS配置
- 生产环境必须使用HTTPS
- 配置SSL证书
- 微信小程序只支持HTTPS请求

## 📊 性能优化

### 服务器端
- **内存配置**: 根据文件大小调整JVM内存
- **并发处理**: 配置合适的线程池大小
- **文件清理**: 自动清理临时文件

### 小程序端
- **进度显示**: 使用 `wx.showLoading` 显示上传进度
- **错误处理**: 完善的错误提示和重试机制
- **文件预检**: 上传前检查文件大小和格式

## � 文件大小限制总结

| 文件大小范围 | 小程序处理方式 | 网页版处理方式 | 推荐方案 |
|-------------|---------------|---------------|---------|
| 0-10MB | 标准上传 (`wx.uploadFile`) | 标准上传 (`FormData`) | 小程序内处理 |
| 10MB-100MB | 分块上传 (`FileSystemManager.readFile`) | 分块上传 (`File.slice`) | 小程序内处理 |
| 100MB以上 | **无法处理** | ✅ 完全支持 | **跳转网页版** |

### 🌐 超大文件解决方案：网页版PDF压缩

#### 网页版优势
- **无文件大小限制**：支持GB级别的PDF文件
- **更强大的分块上传**：可自定义分块大小
- **更好的用户体验**：支持拖拽上传、进度显示
- **更多压缩选项**：不受小程序API限制

#### 网页版技术实现
```javascript
// 网页版分块上传 - 支持超大文件
class WebChunkUploader {
  constructor(file, chunkSize = 10 * 1024 * 1024) { // 10MB分块
    this.file = file;
    this.chunkSize = chunkSize;
    this.totalChunks = Math.ceil(file.size / chunkSize);
  }

  async upload() {
    for (let i = 0; i < this.totalChunks; i++) {
      const start = i * this.chunkSize;
      const end = Math.min(start + this.chunkSize, this.file.size);
      const chunk = this.file.slice(start, end); // 可以处理任意大小

      await this.uploadChunk(chunk, i);
    }
  }

  async uploadChunk(chunk, index) {
    const formData = new FormData();
    formData.append('chunk', chunk);
    formData.append('index', index);
    formData.append('total', this.totalChunks);

    return fetch('/upload-chunk', {
      method: 'POST',
      body: formData
    });
  }
}
```

#### 小程序与网页版结合方案
```javascript
// 小程序中的智能文件处理
const smartFileHandler = async (filePath) => {
  const fileInfo = await getFileInfo(filePath);
  const sizeMB = fileInfo.size / (1024 * 1024);

  if (sizeMB <= 100) {
    // 小程序内处理
    return await processInMiniProgram(filePath);
  } else {
    // 引导到网页版
    wx.showModal({
      title: '使用网页版处理',
      content: `文件大小${sizeMB.toFixed(1)}MB，建议使用网页版获得更好的处理效果。\n\n网页版优势：\n• 支持超大文件\n• 处理速度更快\n• 更多压缩选项`,
      confirmText: '前往网页版',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 复制网页链接
          wx.setClipboardData({
            data: 'https://your-website.com/pdf-compress',
            success: () => {
              wx.showToast({
                title: '链接已复制，请在浏览器中打开',
                icon: 'none',
                duration: 3000
              });
            }
          });
        }
      }
    });
  }
};
```

## �🐛 常见问题

### 1. 文件上传失败
- **10MB以下**：检查服务器域名配置和API密钥
- **10MB-100MB**：确认服务器支持分块上传接口
- **100MB以上**：提示用户文件过大，无法处理

### 2. 压缩效果不理想
- 调整压缩级别参数（1-9）
- 尝试启用灰度压缩
- 设置期望输出大小

### 3. 大文件处理问题
- **分块上传失败**：检查网络稳定性和服务器配置
- **内存不足**：调整分块大小（建议5MB）
- **超过100MB**：引导用户使用网页版处理

### 4. 网页版相关问题
- **如何部署网页版**：可以部署到任何支持Node.js的服务器
- **网页版安全性**：建议使用HTTPS和文件类型验证
- **跨域问题**：配置CORS允许小程序域名访问
- **文件存储**：建议使用云存储服务处理大文件

### 5. 服务器配置问题
- 增加JVM内存配置
- 配置分块上传接口
- 优化临时文件清理机制
- 为网页版配置更大的上传限制

## 🌐 网页版部署指南

### 前端部署
```bash
# 1. 创建网页版项目
mkdir pdf-compress-web
cd pdf-compress-web

# 2. 创建基本HTML结构
cat > index.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>PDF压缩工具 - 网页版</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .upload-area {
            border: 2px dashed #ccc;
            padding: 50px;
            text-align: center;
            cursor: pointer;
            margin: 20px;
        }
        .upload-area.drag-over {
            border-color: #007AFF;
            background-color: #f0f8ff;
        }
        .progress-area {
            margin: 20px;
            display: none;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background-color: #007AFF;
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <h1>PDF压缩工具 - 网页版</h1>
    <div class="upload-area" id="upload-area">
        <p>拖拽PDF文件到这里，或点击选择文件</p>
        <p>支持任意大小的PDF文件（包括超过100MB的大文件）</p>
        <input type="file" id="file-input" accept=".pdf" multiple style="display:none;">
    </div>

    <div class="progress-area" id="progress-area">
        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
        </div>
        <p id="progress-text">准备上传...</p>
    </div>

    <script src="pdf-compressor.js"></script>
</body>
</html>
EOF

# 3. 部署到静态网站托管服务
# 可以使用 Vercel、Netlify、GitHub Pages 等
```

### 后端API扩展
```javascript
// 为网页版添加专门的大文件处理接口
app.post('/web/upload-chunk', upload.single('chunk'), (req, res) => {
  const { index, total } = req.body;
  const chunkPath = `./temp/chunks/${req.sessionID}_${index}`;

  // 保存分块
  fs.writeFileSync(chunkPath, req.file.buffer);

  res.json({
    success: true,
    message: `分块 ${parseInt(index) + 1}/${total} 上传成功`
  });
});

app.post('/web/merge-and-compress', (req, res) => {
  const { sessionId, totalChunks, compressOptions } = req.body;

  // 合并分块
  const mergedFile = mergeChunks(sessionId, totalChunks);

  // 调用Stirling-PDF压缩
  compressPDFFile(mergedFile, compressOptions)
    .then(compressedFile => {
      res.json({
        success: true,
        downloadUrl: `/download/${compressedFile}`
      });
    })
    .catch(error => {
      res.status(500).json({ error: error.message });
    });
});
```

### 域名配置
```javascript
// 小程序需要配置网页版域名
// 在微信公众平台 -> 开发 -> 开发设置 -> 服务器域名中添加：
// request合法域名: https://your-web-domain.com
// uploadFile合法域名: https://your-web-domain.com
// downloadFile合法域名: https://your-web-domain.com
```

## 📝 更新日志

### v1.1.0 (2025-07-25)
- 添加网页版解决方案
- 支持超过100MB的PDF文件处理
- 完善分块上传机制
- 添加智能文件处理引导

### v1.0.0 (2025-07-25)
- 初始版本
- 集成Stirling-PDF后端
- 实现基本压缩功能
- 添加文件大小检查

---

**维护者**: PDF转换乐乐版开发团队
**最后更新**: 2025-07-25
