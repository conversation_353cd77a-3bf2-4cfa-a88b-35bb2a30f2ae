/* pages/index/index.wxss */

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  position: relative;
}

.navbar-title {
  display: none;
}

.navbar-search-capsule {
  position: absolute;
  left: 24rpx;
  right: 0;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  background-color: rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.navbar-search-capsule:active {
  background-color: rgba(0, 0, 0, 0.1);
}

.navbar-search-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 16rpx;
  opacity: 0.5;
}

.navbar-search-text {
  flex: 1;
  font-size: 26rpx;
  color: #888888;
  text-align: left;
}

.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 32rpx;
}



/* 分区样式 */
.section {
  margin-bottom: 48rpx;
}

.section-header {
  display: none;
}

/* 热门功能网格 - 新拟物设计 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  margin-top: 40rpx;
}

.feature-card {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 新拟物设计阴影 */
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);

  /* 磨砂玻璃效果 */
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
}

/* 不同功能卡片的渐变背景叠加 */
.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 32rpx;
  opacity: 0.7;
  z-index: 0;
}

.feature-card.compress {
  animation-delay: 0.1s;
}

.feature-card.compress::before {
  background: linear-gradient(135deg, #DC143C 0%, #FF6B6B 100%);
}

.feature-card.convert {
  animation-delay: 0.2s;
}

.feature-card.convert::before {
  background: linear-gradient(135deg, #2B579A 0%, #4A90E2 100%);
}

.feature-card.unlock {
  animation-delay: 0.3s;
}

.feature-card.unlock::before {
  background: linear-gradient(135deg, #FF6B35 0%, #FFB347 100%);
}

.feature-card.image-to-pdf {
  animation-delay: 0.4s;
}

.feature-card.image-to-pdf::before {
  background: linear-gradient(135deg, #32CD32 0%, #7ED321 100%);
}

.feature-card:active {
  transform: scale(0.98) translateY(2rpx);
  box-shadow:
    6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.6),
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.2),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.feature-icon {
  flex-shrink: 0;
  width: 96rpx;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  position: relative;
  z-index: 1;

  /* 图标容器的新拟物效果 */
  background: rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 0.8),
    inset -2rpx -2rpx 4rpx rgba(163, 177, 198, 0.3);
}

.feature-svg {
  width: 64rpx;
  height: 64rpx;
  opacity: 0.8;
}

/* PDF压缩功能的图标容器特殊样式 */
.feature-card.compress .feature-icon {
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(220, 20, 60, 0.3),
    0 0 8rpx rgba(220, 20, 60, 0.2);
}

/* PDF转Word功能的图标容器特殊样式 */
.feature-card.convert .feature-icon {
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(43, 87, 154, 0.3),
    0 0 8rpx rgba(43, 87, 154, 0.2);
}

/* PDF解密功能的图标容器特殊样式 */
.feature-card.unlock .feature-icon {
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(255, 107, 53, 0.3),
    0 0 8rpx rgba(255, 107, 53, 0.2);
}

/* 图片转PDF功能的图标容器特殊样式 */
.feature-card.image-to-pdf .feature-icon {
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(50, 205, 50, 0.3),
    0 0 8rpx rgba(50, 205, 50, 0.2);
}

.feature-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.feature-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.feature-subtitle {
  font-size: 24rpx;
  color: #5a6c7d;
  line-height: 1.4;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

/* 全部功能列表 - 新拟物风格 */
.function-list {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 24rpx;
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.function-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  margin-bottom: 16rpx;
  border-radius: 24rpx;
  background: #f0f2f5;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow:
    8rpx 8rpx 16rpx rgba(163, 177, 198, 0.4),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
}

.function-item:last-child {
  margin-bottom: 0;
}

.function-item:active {
  transform: scale(0.98);
  box-shadow:
    inset 4rpx 4rpx 8rpx rgba(163, 177, 198, 0.4),
    inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
}

.function-icon {
  width: 72rpx;
  height: 72rpx;
  margin-right: 24rpx;
  padding: 12rpx;
  border-radius: 20rpx;
  background: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    4rpx 4rpx 8rpx rgba(163, 177, 198, 0.4),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8),
    inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 0.9),
    inset -1rpx -1rpx 2rpx rgba(163, 177, 198, 0.2);
  flex-shrink: 0;
}

.function-icon image {
  width: 48rpx;
  height: 48rpx;
  opacity: 0.9;
}

.function-content {
  flex: 1;
}

.function-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.function-subtitle {
  font-size: 24rpx;
  color: #5a6c7d;
  line-height: 1.4;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

/* 功能项特定颜色样式 - 经典Office色系 */
.function-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 24rpx;
  opacity: 0.35;
  z-index: 0;
}

.function-item.merge::before {
  background: linear-gradient(135deg, #8A2BE2 0%, #B19CD9 100%);
}

.function-item.excel::before {
  background: linear-gradient(135deg, #217346 0%, #50C878 100%);
}

.function-item.ppt::before {
  background: linear-gradient(135deg, #D24726 0%, #FF7F50 100%);
}



.function-item.to-image::before {
  background: linear-gradient(135deg, #FF69B4 0%, #FFB6C1 100%);
}

.function-item.split::before {
  background: linear-gradient(135deg, #708090 0%, #B0C4DE 100%);
}

/* 功能图标容器的白色背景样式 */
.function-item.merge .function-icon {
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(138, 43, 226, 0.3),
    0 0 8rpx rgba(138, 43, 226, 0.2);
}

.function-item.excel .function-icon {
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(33, 115, 70, 0.3),
    0 0 8rpx rgba(33, 115, 70, 0.2);
}

.function-item.ppt .function-icon {
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(210, 71, 38, 0.3),
    0 0 8rpx rgba(210, 71, 38, 0.2);
}



.function-item.to-image .function-icon {
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(255, 105, 180, 0.3),
    0 0 8rpx rgba(255, 105, 180, 0.2);
}

.function-item.split .function-icon {
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(112, 128, 144, 0.3),
    0 0 8rpx rgba(112, 128, 144, 0.2);
}
