# PDF拆分功能设计

## 功能概述
PDF拆分功能允许用户将一个PDF文件按照不同的方式拆分成多个独立的PDF文件。

## 设计特点

### 1. 视觉设计
- **新拟物风格**：采用与compress页面一致的新拟物设计风格
- **渐变色彩**：使用红色渐变作为主色调，绿色作为成功状态色
- **阴影效果**：多层阴影营造立体感
- **圆角设计**：统一的圆角半径保持设计一致性

### 2. 交互设计
- **直观操作**：点击上传区域选择PDF文件
- **模态弹窗**：拆分方式选择使用模态弹窗
- **实时预估**：根据设置实时显示预估结果
- **进度反馈**：拆分过程中显示进度条和百分比

### 3. 功能特性

#### 拆分方式
1. **按页数拆分**
   - 用户可以设置每个文件包含的页数
   - 滑块控制，范围从1页到总页数
   - 实时显示将生成的文件数量

2. **按页码范围拆分**
   - 用户可以指定起始页和结束页
   - 输入框支持数字输入
   - 自动验证页码范围的有效性

3. **拆分为单页**
   - 将PDF的每一页都拆分为独立文件
   - 一键操作，无需额外设置

#### 预估功能
- 显示将生成的文件数量
- 显示每个文件的页数（按页数拆分模式）
- 显示提取的总页数（按范围拆分模式）

#### 结果展示
- 列表显示所有生成的文件
- 每个文件显示名称和页数
- 支持单个文件下载和批量下载

### 4. 技术实现

#### 文件结构
```
pages/pdf-split/
├── pdf-split.wxml    # 页面结构
├── pdf-split.wxss    # 样式文件
├── pdf-split.js      # 逻辑处理
├── pdf-split.json    # 页面配置
└── README.md         # 说明文档
```

#### 核心功能
- **文件选择**：使用wx.chooseMessageFile API
- **页数获取**：模拟PDF页数解析（实际需要PDF解析库）
- **进度模拟**：使用定时器模拟拆分进度
- **结果生成**：根据拆分方式生成文件列表

#### 状态管理
- `fileSelected`: 是否已选择文件
- `splitting`: 是否正在拆分
- `splitted`: 是否拆分完成
- `splitMode`: 当前拆分方式
- `canSplit`: 是否可以开始拆分

### 5. 用户体验

#### 操作流程
1. 进入页面自动触发文件选择
2. 选择PDF文件后显示文件信息
3. 选择拆分方式并进行设置
4. 查看预估结果
5. 点击开始拆分
6. 查看拆分进度
7. 下载拆分结果

#### 反馈机制
- 文件选择成功/失败提示
- 设置验证和错误提示
- 拆分进度实时显示
- 拆分完成成功提示

### 6. 响应式设计
- 适配不同屏幕尺寸
- 底部固定操作区域
- 滚动区域合理分配
- 安全区域适配

### 7. 可扩展性
- 支持更多拆分方式（如按书签、按大小等）
- 支持批量文件拆分
- 支持自定义文件命名规则
- 支持云端处理和存储

## 设计亮点

1. **一致性**：与现有compress页面保持设计风格一致
2. **易用性**：简化操作流程，减少用户学习成本
3. **反馈性**：每个操作都有明确的视觉反馈
4. **灵活性**：提供多种拆分方式满足不同需求
5. **美观性**：新拟物设计风格现代且优雅
6. **专业性**：使用纯CSS图标替代emoji，界面更加专业

## 图标设计改进

### 问题
- 原始设计使用emoji图标（📄、📋、📃）
- emoji在不同设备上显示不一致
- 看起来不够专业和高级

### 解决方案
- 使用纯CSS创建的几何图标
- 保持新拟物设计风格
- 支持主题色彩变化
- 在选中状态下自动适配白色

### 图标设计
1. **按页数拆分**：堆叠的文档图标，表示多个文件
2. **按页码范围拆分**：带选择框的文档，表示范围选择
3. **拆分为单页**：分离的两个页面，表示单页拆分

## 后续优化建议

1. **性能优化**：大文件处理的性能优化
2. **功能扩展**：添加更多拆分选项
3. **云端集成**：支持云端PDF处理服务
4. **批量操作**：支持多文件同时拆分
5. **预览功能**：添加PDF页面预览功能
