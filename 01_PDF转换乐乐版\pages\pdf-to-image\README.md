# PDF转图片功能

## 功能概述
PDF转图片功能允许用户将PDF文件的每一页转换为独立的图片文件，支持JPG和PNG两种格式。

## 主要特性

### 1. 文件选择
- 支持选择PDF文件
- 显示文件基本信息（文件名、大小、页数）
- 自动检测PDF页数

### 2. 转换设置
- **输出格式选择**：支持JPG和PNG两种格式
  - JPG：适合照片，文件较小
  - PNG：支持透明背景，质量更高
- **图片质量调节**：50%-100%可调节
- **页面范围选择**：
  - 全部页面：转换PDF的所有页面
  - 自定义范围：指定起始页和结束页

### 3. 转换过程
- 实时显示转换进度
- 进度条动画效果
- 转换状态提示

### 4. 结果展示
- 显示转换完成的图片数量
- 图片预览功能
- 支持点击预览大图
- 提供下载功能

## UI设计特点

### 1. 导航栏
- 自定义导航栏设计
- 返回按钮和页面标题
- 适配不同设备的状态栏高度

### 2. 文件上传区域
- 新拟物化设计风格
- 虚线边框和阴影效果
- 点击动画反馈

### 3. 设置面板
- 固定在底部的设置区域
- 滑块控制图片质量
- 格式选择弹窗
- 页面范围选择器

### 4. 进度显示
- 圆形进度图标
- 渐变进度条
- 百分比数字显示

### 5. 结果展示
- 成功图标和提示
- 横向滚动的图片预览
- 操作按钮（重新转换、下载）

### 6. 底部胶囊按钮
- 固定底部的操作按钮
- 左侧：选择PDF
- 右侧：下载图片（转换完成后启用）

## 技术实现

### 1. 页面结构
- `pdf-to-image.wxml`：页面结构
- `pdf-to-image.js`：页面逻辑
- `pdf-to-image.wxss`：页面样式
- `pdf-to-image.json`：页面配置

### 2. 主要功能函数
- `onSelectFile()`：选择PDF文件
- `getPDFPageCount()`：获取PDF页数
- `onConvertToPDF()`：开始转换
- `simulateConversion()`：模拟转换过程
- `onConversionComplete()`：转换完成处理
- `onDownloadImages()`：下载图片

### 3. 状态管理
- 文件选择状态
- 转换进行状态
- 转换完成状态
- 各种设置参数

## 样式特色

### 1. 新拟物化设计
- 柔和的阴影效果
- 内凹外凸的视觉层次
- 自然的光影变化

### 2. 色彩搭配
- 主色调：#DC143C（深红色）
- 背景色：#f8f9fa（浅灰色）
- 文字色：#1a1a1a（深灰色）

### 3. 交互动画
- 按钮点击缩放效果
- 进度条平滑过渡
- 弹窗淡入淡出

## 使用流程

1. **选择文件**：点击上传区域选择PDF文件
2. **设置参数**：选择输出格式、调节图片质量、设置页面范围
3. **开始转换**：点击"开始转换"按钮
4. **等待完成**：查看转换进度
5. **预览下载**：预览转换结果并下载图片

## 注意事项

1. 目前为演示版本，实际转换功能需要集成真实的PDF处理API
2. 图片预览使用的是示例图片，实际使用时需要显示真实转换结果
3. 下载功能需要根据平台特性进行适配
4. 页数检测功能需要真实的PDF解析库支持
