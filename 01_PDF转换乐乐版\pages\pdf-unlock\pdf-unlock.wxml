<!--pages/pdf-unlock/pdf-unlock.wxml-->
<!-- 自定义导航栏 -->
<view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
  <view class="navbar-content">
    <view class="navbar-back" bindtap="onBackClick" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx;">
      <image class="navbar-back-icon" src="/images/back.svg"></image>
    </view>
    <view class="navbar-title" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx; line-height: {{menuButtonHeightRpx}}rpx;">PDF解密</view>
  </view>
</view>

<!-- 密码输入弹窗 -->
<view class="password-modal" wx:if="{{showPasswordModal}}" bindtap="hidePasswordModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">🔐 输入PDF密码</text>
      <view class="modal-close" bindtap="hidePasswordModal">
        <text class="close-icon">✕</text>
      </view>
    </view>

    <view class="modal-body">
      <view class="input-section">
        <text class="input-label">请输入PDF文件的密码</text>
        <view class="input-container">
          <input
            class="password-input"
            placeholder="请输入密码"
            value="{{inputPassword}}"
            bindinput="onPasswordInput"
            focus="{{inputFocus}}"
            password="{{!showPassword}}"
          />
          <view class="password-toggle" bindtap="togglePasswordVisibility">
            <text class="toggle-icon">{{showPassword ? '👁️' : '👁️‍🗨️'}}</text>
          </view>
        </view>
        <text class="input-tip">💡 请输入正确的PDF密码以解锁文件</text>
      </view>
    </view>

    <view class="modal-footer">
      <view class="modal-btn cancel-btn" bindtap="hidePasswordModal">
        <text>取消</text>
      </view>
      <view class="modal-btn confirm-btn" bindtap="confirmPassword">
        <text>确定</text>
      </view>
    </view>
  </view>
</view>

<!-- 页面内容 -->
<view class="container" style="padding-top: {{navbarHeight}}px;">
  
  <!-- 文件上传区域 -->
  <view class="upload-section" wx:if="{{!fileSelected}}">
    <view class="upload-area" bindtap="onSelectFile">
      <view class="upload-icon">
        <image class="upload-svg" src="/images/unlock.svg"></image>
      </view>
      <view class="upload-content">
        <view class="upload-title">选择加密PDF文件</view>
        <view class="upload-subtitle">点击选择或拖拽文件到此处</view>
        <view class="upload-tips">支持加密的PDF格式</view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content" wx:if="{{fileSelected && !unlocking}}">
    <!-- 多文件显示 -->
    <view wx:if="{{selectedFiles.length > 1}}">
      <view class="file-info-card" wx:for="{{selectedFiles}}" wx:key="id">
        <image class="file-icon" src="/images/pdf-unlock-icon.svg"></image>
        <view class="file-details">
          <view class="file-name">{{item.name}}</view>
          <view class="file-status-info">
            <text class="status-label">状态：</text>
            <text class="status-value {{item.unlocked ? 'unlocked' : 'locked'}}">{{item.unlocked ? '已解锁' : '已加密'}}</text>
            <text class="status-separator">　</text>
            <text class="size-label">大小：</text>
            <text class="size-value">{{item.size}}</text>
          </view>
        </view>

      </view>
    </view>

    <!-- 单文件显示（保持原有逻辑） -->
    <view wx:if="{{selectedFiles.length <= 1}}">
      <view class="file-info-card">
        <image class="file-icon" src="/images/pdf-unlock-icon.svg"></image>
        <view class="file-details">
          <view class="file-name">{{fileName}}</view>
          <view class="file-status-info">
            <text class="status-label">状态：</text>
            <text class="status-value {{unlocked ? 'unlocked' : 'locked'}}">{{unlocked ? '已解锁' : '已加密'}}</text>
            <text class="status-separator">　</text>
            <text class="size-label">大小：</text>
            <text class="size-value">{{fileSize}}</text>
          </view>
        </view>

      </view>
    </view>

  </view>



  <!-- 解锁进行中 -->
  <view class="unlocking-section" wx:if="{{unlocking}}">
    <view class="progress-card">
      <view class="progress-icon">
        <image class="progress-svg rotating" src="/images/unlock.svg"></image>
      </view>
      <view class="progress-content">
        <view class="progress-title">正在解锁PDF...</view>
        <view class="progress-subtitle">请稍候，正在为您移除密码保护</view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercent}}%"></view>
        </view>
        <view class="progress-text">{{progressPercent}}%</view>
      </view>
    </view>
  </view>

  <!-- 解锁完成 -->
  <view class="result-section" wx:if="{{unlocked}}">
    <view class="result-card">
      <view class="result-icon">
        <image class="result-svg" src="/images/unlock.svg"></image>
      </view>
      <view class="result-content">
        <view class="result-title">解锁完成！</view>
        <view class="result-subtitle">PDF文件已成功解锁，密码保护已移除</view>

        <view class="result-actions">
          <view class="action-btn secondary" bindtap="onUnlockAnother">
            <text>解锁其他文件</text>
          </view>
          <view class="action-btn primary" bindtap="onDownloadPDF">
            <text>下载解锁文件</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部胶囊按钮 -->
  <view class="bottom-capsule">
    <view class="capsule-btn left" bindtap="onSelectPDF">
      <view class="capsule-text">选择PDF</view>
    </view>
    <view class="capsule-btn right {{!unlocked ? 'disabled' : ''}}" bindtap="onDownloadPDF">
      <view class="capsule-text">下载PDF</view>
    </view>
  </view>

</view>
