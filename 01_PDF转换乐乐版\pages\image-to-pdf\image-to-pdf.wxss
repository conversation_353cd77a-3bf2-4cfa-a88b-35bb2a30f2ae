/* pages/image-to-pdf/image-to-pdf.wxss */

/* 新拟物设计 + 磨砂玻璃风格 */

/* 自定义导航栏样式 - 磨砂玻璃效果 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.05);
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  position: relative;
}

.navbar-back {
  position: absolute;
  left: 32rpx;
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 32rpx;
  background: linear-gradient(145deg, #f0f0f0, #ffffff);
  box-shadow:
    6rpx 6rpx 12rpx rgba(0, 0, 0, 0.1),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.navbar-back:active {
  box-shadow:
    inset 3rpx 3rpx 6rpx rgba(0, 0, 0, 0.1),
    inset -3rpx -3rpx 6rpx rgba(255, 255, 255, 0.8);
  transform: scale(0.95);
}

.navbar-back-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.7;
}

.navbar-title {
  position: absolute;
  font-size: 34rpx;
  font-weight: 600;
  color: #333333;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 页面容器 - 渐变背景 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: 200rpx;
}

/* 文件上传区域 - 新拟物设计 */
.upload-section {
  padding: 60rpx 32rpx;
}

.upload-area {
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  border-radius: 32rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  box-shadow:
    20rpx 20rpx 40rpx rgba(0, 0, 0, 0.1),
    -20rpx -20rpx 40rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 2rpx rgba(255, 255, 255, 0.3);
  border: 2rpx solid transparent;
  background-clip: padding-box;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.upload-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 32rpx;
}

.upload-area:active {
  transform: scale(0.98);
  box-shadow:
    inset 10rpx 10rpx 20rpx rgba(0, 0, 0, 0.1),
    inset -10rpx -10rpx 20rpx rgba(255, 255, 255, 0.8);
}

.upload-area:active::before {
  opacity: 1;
}

.upload-icon {
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;
}

.upload-svg {
  width: 120rpx;
  height: 120rpx;
  filter: drop-shadow(0 4rpx 8rpx rgba(76, 175, 80, 0.2));
}

.upload-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 1;
}

.upload-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 12rpx;
  position: relative;
  z-index: 1;
}

.upload-tips {
  font-size: 24rpx;
  color: #999999;
  position: relative;
  z-index: 1;
}

/* 图片列表区域 - 磨砂玻璃效果 */
.images-section {
  padding: 0 32rpx 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 0 8rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  text-shadow: 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.add-more-btn {
  font-size: 28rpx;
  color: #4CAF50;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background: rgba(76, 175, 80, 0.1);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(76, 175, 80, 0.2);
  box-shadow:
    4rpx 4rpx 8rpx rgba(0, 0, 0, 0.1),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.add-more-btn:active {
  transform: scale(0.95);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.1),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.images-list {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  overflow: hidden;
}

.image-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: background-color 0.3s ease;
}

.image-item:last-child {
  border-bottom: none;
}

.image-item:active {
  background: rgba(76, 175, 80, 0.05);
}

.image-preview {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  overflow: hidden;
  margin-right: 24rpx;
  box-shadow:
    8rpx 8rpx 16rpx rgba(0, 0, 0, 0.1),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8);
  border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.preview-img {
  width: 100%;
  height: 100%;
}

.image-index {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10rpx);
  -webkit-backdrop-filter: blur(10rpx);
  color: #ffffff;
  font-size: 20rpx;
  padding: 6rpx 10rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

.image-info {
  flex: 1;
  margin-right: 24rpx;
}

.image-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.image-size {
  font-size: 24rpx;
  color: #666666;
  opacity: 0.8;
}

.image-actions {
  display: flex;
  gap: 8rpx;
}

.action-btn {
  width: 56rpx;
  height: 56rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.action-btn.move-up,
.action-btn.move-down {
  background: linear-gradient(145deg, #f8f9fa, #e9ecef);
  color: #666666;
  box-shadow:
    4rpx 4rpx 8rpx rgba(0, 0, 0, 0.1),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
}

.action-btn.move-up:active,
.action-btn.move-down:active {
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.1),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
  transform: scale(0.95);
}

.action-btn.remove {
  background: linear-gradient(145deg, #ffebee, #ffcdd2);
  color: #f44336;
  box-shadow:
    4rpx 4rpx 8rpx rgba(244, 67, 54, 0.2),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
}

.action-btn.remove:active {
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(244, 67, 54, 0.2),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
  transform: scale(0.95);
}

/* 设置选项区域 - 新拟物设计 */
.settings-section {
  padding: 0 32rpx 32rpx;
}

.setting-group {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 8rpx 32rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  overflow: hidden;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx 16rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
}

.setting-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.setting-value {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.5);
}

.setting-options {
  padding: 0 24rpx 24rpx;
}

.setting-options.horizontal {
  display: flex;
  gap: 16rpx;
}

.option-item {
  padding: 24rpx;
  border-radius: 16rpx;
  background: linear-gradient(145deg, #f8f9fa, #ffffff);
  box-shadow:
    6rpx 6rpx 12rpx rgba(0, 0, 0, 0.1),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  margin-bottom: 16rpx;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.setting-options.horizontal .option-item {
  flex: 1;
  margin-bottom: 0;
  text-align: center;
}

.option-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 16rpx;
}

.option-item.active {
  background: linear-gradient(145deg, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
  box-shadow:
    inset 3rpx 3rpx 6rpx rgba(76, 175, 80, 0.2),
    inset -3rpx -3rpx 6rpx rgba(255, 255, 255, 0.8),
    0 0 0 2rpx rgba(76, 175, 80, 0.3);
}

.option-item.active::before {
  opacity: 1;
}

.option-item:active {
  transform: scale(0.98);
}

.option-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  position: relative;
  z-index: 1;
  transition: color 0.3s ease;
}

.option-item.active .option-text {
  color: #4CAF50;
  font-weight: 600;
}

.option-desc {
  font-size: 24rpx;
  color: #666666;
  margin-top: 8rpx;
  display: block;
  position: relative;
  z-index: 1;
  opacity: 0.8;
}

.option-item.active .option-desc {
  color: #4CAF50;
  opacity: 0.9;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx 16rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
}

.setting-switch {
  transform: scale(0.9);
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.setting-desc {
  padding: 0 24rpx 24rpx;
  font-size: 24rpx;
  color: #666666;
  opacity: 0.8;
  line-height: 1.4;
}

/* 转换进行中样式 - 磨砂玻璃效果 */
.converting-section {
  padding: 60rpx 32rpx;
}

.progress-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(30rpx);
  -webkit-backdrop-filter: blur(30rpx);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 20rpx 60rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.progress-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.05), rgba(76, 175, 80, 0.02));
  border-radius: 32rpx;
}

.progress-icon {
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;
}

.progress-svg {
  width: 120rpx;
  height: 120rpx;
  filter: drop-shadow(0 8rpx 16rpx rgba(76, 175, 80, 0.3));
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.progress-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 1;
}

.progress-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: linear-gradient(145deg, #e9ecef, #f8f9fa);
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.1),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 1;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #66BB6A);
  border-radius: 6rpx;
  transition: width 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 0 10rpx rgba(76, 175, 80, 0.5);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 1.5s ease-in-out infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-text {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 1;
}

/* 转换完成样式 - 新拟物设计 */
.result-section {
  padding: 60rpx 32rpx;
}

.result-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(30rpx);
  -webkit-backdrop-filter: blur(30rpx);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 20rpx 60rpx rgba(0, 0, 0, 0.1),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.result-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.05), rgba(76, 175, 80, 0.02));
  border-radius: 32rpx;
}

.result-icon {
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;
}

.result-svg {
  width: 120rpx;
  height: 120rpx;
  filter: drop-shadow(0 8rpx 16rpx rgba(76, 175, 80, 0.3));
  animation: success-bounce 0.6s ease-out;
}

@keyframes success-bounce {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); opacity: 1; }
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
  text-shadow: 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
  position: relative;
  z-index: 1;
}

.result-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}

/* PDF预览 - 新拟物设计 */
.pdf-preview {
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.preview-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.preview-info {
  font-size: 24rpx;
  color: #666666;
  opacity: 0.8;
}

.preview-container {
  width: 100%;
  height: 300rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background: linear-gradient(145deg, #f8f9fa, #ffffff);
  box-shadow:
    inset 8rpx 8rpx 16rpx rgba(0, 0, 0, 0.1),
    inset -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.pdf-preview-img {
  width: 100%;
  height: 100%;
}

.result-actions {
  display: flex;
  gap: 24rpx;
  position: relative;
  z-index: 1;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-btn:active::before {
  opacity: 1;
}

.action-btn.secondary {
  background: linear-gradient(145deg, #f0f0f0, #ffffff);
  color: #666666;
  box-shadow:
    8rpx 8rpx 16rpx rgba(0, 0, 0, 0.1),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8);
}

.action-btn.secondary:active {
  box-shadow:
    inset 4rpx 4rpx 8rpx rgba(0, 0, 0, 0.1),
    inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
  transform: scale(0.98);
}

.action-btn.primary {
  background: linear-gradient(145deg, #4CAF50, #66BB6A);
  color: #ffffff;
  box-shadow:
    8rpx 8rpx 16rpx rgba(76, 175, 80, 0.3),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.action-btn.primary:active {
  box-shadow:
    inset 4rpx 4rpx 8rpx rgba(76, 175, 80, 0.3),
    inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
  transform: scale(0.98);
}

/* 底部胶囊按钮 - 磨砂玻璃效果 */
.bottom-capsule {
  position: fixed;
  bottom: 60rpx;
  left: 32rpx;
  right: 32rpx;
  display: flex;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(30rpx);
  -webkit-backdrop-filter: blur(30rpx);
  border-radius: 50rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 20rpx 60rpx rgba(0, 0, 0, 0.15),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  overflow: hidden;
  z-index: 1000;
}

.capsule-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.capsule-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.capsule-btn:active::before {
  opacity: 1;
}

.capsule-btn.left {
  background: linear-gradient(145deg, rgba(248, 249, 250, 0.8), rgba(255, 255, 255, 0.6));
  color: #666666;
  border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}

.capsule-btn.left:active {
  background: linear-gradient(145deg, rgba(240, 240, 240, 0.8), rgba(248, 249, 250, 0.6));
  transform: scale(0.98);
}

.capsule-btn.right {
  background: linear-gradient(145deg, #4CAF50, #66BB6A);
  color: #ffffff;
  box-shadow: inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
}

.capsule-btn.right:active {
  background: linear-gradient(145deg, #45a049, #4CAF50);
  transform: scale(0.98);
}

.capsule-btn.disabled {
  background: linear-gradient(145deg, rgba(224, 224, 224, 0.8), rgba(240, 240, 240, 0.6));
  color: #999999;
  cursor: not-allowed;
}

.capsule-btn.disabled:active {
  transform: none;
}

.capsule-text {
  font-size: 32rpx;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.capsule-btn.left .capsule-text {
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}
