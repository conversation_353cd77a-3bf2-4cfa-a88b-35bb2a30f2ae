/* pages/image-to-pdf/image-to-pdf.wxss */

/* 新拟物设计风格 - 与compress页面保持一致 */

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  position: relative;
}

.navbar-back {
  position: absolute;
  left: 32rpx;
  width: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.navbar-back:active {
  opacity: 0.6;
  transform: scale(0.9);
}

.navbar-back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  position: absolute;
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 页面容器 */
.container {
  min-height: 100vh;
  background-color: #f0f2f5;
  padding: 32rpx;
  margin-top: 30rpx;
}

/* 文件上传区域 */
.upload-section {
  margin-bottom: 48rpx;
}

.upload-area {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 80rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 4rpx dashed rgba(163, 177, 198, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 新拟物设计阴影 */
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.upload-area:active {
  transform: scale(0.98);
  box-shadow:
    inset 6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    inset -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.upload-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(76, 175, 80, 0.3),
    0 0 8rpx rgba(76, 175, 80, 0.2);
}

.upload-svg {
  width: 80rpx;
  height: 80rpx;
  opacity: 0.8;
}

.upload-content {
  flex: 1;
}

.upload-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.upload-subtitle {
  font-size: 28rpx;
  color: #5a6c7d;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.upload-tips {
  font-size: 24rpx;
  color: #8a9aa9;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

/* 图片列表区域 */
.images-section {
  margin-bottom: 48rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.add-more-btn {
  font-size: 28rpx;
  color: #4CAF50;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  background: #f0f2f5;
  box-shadow:
    6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.add-more-btn:active {
  transform: scale(0.95);
  box-shadow:
    inset 3rpx 3rpx 6rpx rgba(163, 177, 198, 0.4),
    inset -3rpx -3rpx 6rpx rgba(255, 255, 255, 0.8);
}

.images-list {
  background: #f0f2f5;
  border-radius: 24rpx;
  box-shadow:
    8rpx 8rpx 16rpx rgba(163, 177, 198, 0.4),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.image-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(163, 177, 198, 0.2);
  transition: background-color 0.3s ease;
}

.image-item:last-child {
  border-bottom: none;
}

.image-item:active {
  background: rgba(76, 175, 80, 0.05);
}

.image-preview {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
  box-shadow:
    4rpx 4rpx 8rpx rgba(163, 177, 198, 0.4),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
}

.preview-img {
  width: 100%;
  height: 100%;
}

.image-index {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-weight: 600;
}

.image-info {
  flex: 1;
  margin-right: 24rpx;
}

.image-name {
  font-size: 28rpx;
  color: #2c3e50;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.image-size {
  font-size: 24rpx;
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.image-actions {
  display: flex;
  gap: 8rpx;
}

.action-btn {
  width: 56rpx;
  height: 56rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn.move-up,
.action-btn.move-down {
  background: #f0f2f5;
  color: #5a6c7d;
  box-shadow:
    4rpx 4rpx 8rpx rgba(163, 177, 198, 0.4),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
}

.action-btn.move-up:active,
.action-btn.move-down:active {
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.4),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
  transform: scale(0.95);
}

.action-btn.remove {
  background: #f0f2f5;
  color: #DC143C;
  box-shadow:
    4rpx 4rpx 8rpx rgba(163, 177, 198, 0.4),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
}

.action-btn.remove:active {
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.4),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
  transform: scale(0.95);
}

/* 设置选项区域 */
.settings-section {
  margin-bottom: 48rpx;
}

.setting-group {
  background: #f0f2f5;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow:
    8rpx 8rpx 16rpx rgba(163, 177, 198, 0.4),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx 16rpx;
}

.setting-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.setting-value {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.setting-options {
  padding: 0 24rpx 24rpx;
}

.setting-options.horizontal {
  display: flex;
  gap: 16rpx;
}

.option-item {
  padding: 24rpx;
  border-radius: 16rpx;
  background: #f0f2f5;
  box-shadow:
    6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.setting-options.horizontal .option-item {
  flex: 1;
  margin-bottom: 0;
  text-align: center;
}

.option-item.active {
  box-shadow:
    inset 3rpx 3rpx 6rpx rgba(163, 177, 198, 0.4),
    inset -3rpx -3rpx 6rpx rgba(255, 255, 255, 0.8),
    0 0 0 2rpx rgba(76, 175, 80, 0.3);
}

.option-item:active {
  transform: scale(0.98);
}

.option-text {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 600;
  transition: color 0.3s ease;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.option-item.active .option-text {
  color: #4CAF50;
}

.option-desc {
  font-size: 24rpx;
  color: #5a6c7d;
  margin-top: 8rpx;
  display: block;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.option-item.active .option-desc {
  color: #4CAF50;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx 16rpx;
}

.setting-switch {
  transform: scale(0.8);
}

.setting-desc {
  padding: 0 24rpx 24rpx;
  font-size: 24rpx;
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

/* 转换进行中样式 */
.converting-section {
  margin-bottom: 48rpx;
}

.progress-card {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.progress-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(76, 175, 80, 0.3),
    0 0 8rpx rgba(76, 175, 80, 0.2);
}

.progress-svg {
  width: 80rpx;
  height: 80rpx;
  opacity: 0.8;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.progress-content {
  width: 100%;
}

.progress-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.progress-subtitle {
  font-size: 26rpx;
  color: #5a6c7d;
  margin-bottom: 40rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: rgba(163, 177, 198, 0.3);
  border-radius: 6rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.4),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50 0%, #66BB6A 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
  box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.3);
}

.progress-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #4CAF50;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 转换完成样式 */
.result-section {
  margin-bottom: 48rpx;
}

.result-card {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.result-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.result-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  padding: 16rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(76, 175, 80, 0.3),
    0 0 8rpx rgba(76, 175, 80, 0.2);
}

.result-svg {
  width: 48rpx;
  height: 48rpx;
  opacity: 0.8;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #4CAF50;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
  margin-bottom: 16rpx;
}

.result-subtitle {
  font-size: 28rpx;
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
  margin-bottom: 40rpx;
}

/* PDF预览 */
.pdf-preview {
  margin-bottom: 40rpx;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.preview-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.preview-info {
  font-size: 24rpx;
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.preview-container {
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f0f2f5;
  box-shadow:
    inset 4rpx 4rpx 8rpx rgba(163, 177, 198, 0.4),
    inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
}

.pdf-preview-img {
  width: 100%;
  height: 100%;
}

.result-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: #f0f2f5;
  color: #5a6c7d;
  box-shadow:
    8rpx 8rpx 16rpx rgba(163, 177, 198, 0.4),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8);
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.action-btn.secondary:active {
  box-shadow:
    inset 4rpx 4rpx 8rpx rgba(163, 177, 198, 0.4),
    inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
  transform: scale(0.95);
}

.action-btn.primary {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  color: white;
  box-shadow:
    8rpx 8rpx 16rpx rgba(76, 175, 80, 0.3),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.action-btn.primary:active {
  box-shadow:
    inset 4rpx 4rpx 8rpx rgba(76, 175, 80, 0.3),
    inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
  transform: scale(0.95);
}

/* 底部胶囊按钮 */
.bottom-capsule {
  position: fixed;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 24rpx;
  z-index: 1000;
}

.capsule-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 17rpx 48rpx;
  border-radius: 42rpx;
  box-shadow:
    0 6rpx 24rpx rgba(0, 0, 0, 0.12),
    0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  min-width: 240rpx;
  height: 55rpx;
}

.capsule-btn:active {
  transform: scale(0.96);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.16),
    0 1rpx 2rpx rgba(0, 0, 0, 0.12);
}

.capsule-btn.left {
  background: linear-gradient(135deg, #5a6c7d 0%, #8a9aa9 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(90, 108, 125, 0.3),
    0 2rpx 6rpx rgba(90, 108, 125, 0.2);
}

.capsule-btn.right {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(76, 175, 80, 0.3),
    0 2rpx 6rpx rgba(76, 175, 80, 0.2);
}

.capsule-btn.disabled {
  background: linear-gradient(135deg, #CCCCCC 0%, #999999 100%);
  color: rgba(255, 255, 255, 0.7);
  pointer-events: none;
  box-shadow:
    0 6rpx 24rpx rgba(0, 0, 0, 0.12),
    0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.capsule-text {
  font-size: 32rpx;
  font-weight: 500;
  line-height: 1;
}
