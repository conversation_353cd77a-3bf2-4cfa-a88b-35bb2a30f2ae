/* pages/image-to-pdf/image-to-pdf.wxss */

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  position: relative;
}

.navbar-back {
  position: absolute;
  left: 32rpx;
  width: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.navbar-back:active {
  opacity: 0.6;
  transform: scale(0.9);
}

.navbar-back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  position: absolute;
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 页面容器 */
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 200rpx;
}

/* 文件上传区域 */
.upload-section {
  padding: 60rpx 32rpx;
}

.upload-area {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  border: 2rpx dashed #e0e0e0;
  transition: all 0.3s ease;
}

.upload-area:active {
  transform: scale(0.98);
  border-color: #4CAF50;
}

.upload-icon {
  margin-bottom: 32rpx;
}

.upload-svg {
  width: 120rpx;
  height: 120rpx;
}

.upload-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.upload-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 12rpx;
}

.upload-tips {
  font-size: 24rpx;
  color: #999999;
}

/* 图片列表区域 */
.images-section {
  padding: 0 32rpx 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.add-more-btn {
  font-size: 28rpx;
  color: #4CAF50;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  background-color: rgba(76, 175, 80, 0.1);
}

.images-list {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
}

.image-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.image-item:last-child {
  border-bottom: none;
}

.image-preview {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 24rpx;
}

.preview-img {
  width: 100%;
  height: 100%;
}

.image-index {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.image-info {
  flex: 1;
  margin-right: 24rpx;
}

.image-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-size {
  font-size: 24rpx;
  color: #999999;
}

.image-actions {
  display: flex;
  gap: 8rpx;
}

.action-btn {
  width: 56rpx;
  height: 56rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
}

.action-btn.move-up,
.action-btn.move-down {
  background-color: #f0f0f0;
  color: #666666;
}

.action-btn.remove {
  background-color: #ffebee;
  color: #f44336;
}

/* 设置选项区域 */
.settings-section {
  padding: 0 32rpx 32rpx;
}

.setting-group {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx 16rpx;
}

.setting-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.setting-value {
  font-size: 28rpx;
  color: #4CAF50;
}

.setting-options {
  padding: 0 24rpx 24rpx;
}

.setting-options.horizontal {
  display: flex;
  gap: 16rpx;
}

.option-item {
  padding: 24rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e0e0e0;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}

.setting-options.horizontal .option-item {
  flex: 1;
  margin-bottom: 0;
  text-align: center;
}

.option-item.active {
  border-color: #4CAF50;
  background-color: rgba(76, 175, 80, 0.05);
}

.option-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.option-item.active .option-text {
  color: #4CAF50;
}

.option-desc {
  font-size: 24rpx;
  color: #999999;
  margin-top: 8rpx;
  display: block;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx 16rpx;
}

.setting-switch {
  transform: scale(0.8);
}

.setting-desc {
  padding: 0 24rpx 24rpx;
  font-size: 24rpx;
  color: #999999;
}

/* 转换进行中样式 */
.converting-section {
  padding: 60rpx 32rpx;
}

.progress-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
}

.progress-icon {
  margin-bottom: 32rpx;
}

.progress-svg {
  width: 120rpx;
  height: 120rpx;
}

.progress-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.progress-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 40rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  background-color: #4CAF50;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: 600;
}

/* 转换完成样式 */
.result-section {
  padding: 60rpx 32rpx;
}

.result-card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
}

.result-icon {
  margin-bottom: 32rpx;
}

.result-svg {
  width: 120rpx;
  height: 120rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.result-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 40rpx;
}

/* PDF预览 */
.pdf-preview {
  margin-bottom: 40rpx;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.preview-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.preview-info {
  font-size: 24rpx;
  color: #999999;
}

.preview-container {
  width: 100%;
  height: 300rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f8f9fa;
}

.pdf-preview-img {
  width: 100%;
  height: 100%;
}

.result-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background-color: #f0f0f0;
  color: #666666;
}

.action-btn.primary {
  background-color: #4CAF50;
  color: #ffffff;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 底部胶囊按钮 */
.bottom-capsule {
  position: fixed;
  bottom: 60rpx;
  left: 32rpx;
  right: 32rpx;
  display: flex;
  background-color: #ffffff;
  border-radius: 50rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  z-index: 1000;
}

.capsule-btn {
  flex: 1;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.capsule-btn.left {
  background-color: #f8f9fa;
  color: #666666;
}

.capsule-btn.right {
  background-color: #4CAF50;
  color: #ffffff;
}

.capsule-btn.disabled {
  background-color: #e0e0e0;
  color: #999999;
}

.capsule-btn:active {
  opacity: 0.8;
}

.capsule-text {
  font-size: 32rpx;
  font-weight: 600;
}
