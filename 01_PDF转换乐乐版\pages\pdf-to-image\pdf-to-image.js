// pages/pdf-to-image/pdf-to-image.js
Page({
  data: {
    statusBarHeight: 0,
    navbarHeight: 0,
    menuButtonTopRpx: 0,
    menuButtonHeightRpx: 0,
    
    // 文件状态
    fileSelected: false,
    converting: false,
    converted: false,
    
    // 文件信息
    fileName: '',
    fileSize: '',
    filePath: '',
    selectedFiles: [], // 多文件选择

    // 转换选项（固定设置）
    selectedFormat: 'png', // 固定为PNG格式
    imageQuality: 90, // 固定为90%质量
    pageRange: 'all', // all, custom
    pageRangeText: '全部页面',
    customPageRange: '',



    // 转换进度
    progressPercent: 0,
    
    // 转换结果
    convertedImages: [],
    convertedImageCount: 0
  },

  onLoad(options) {
    this.initNavbar()
    // 页面加载后直接选择PDF文件
    setTimeout(() => {
      this.onSelectPDF()
    }, 500)
  },

  // 初始化导航栏
  initNavbar() {
    const systemInfo = wx.getSystemInfoSync()
    const menuButton = wx.getMenuButtonBoundingClientRect()
    
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navbarHeight: systemInfo.statusBarHeight + 44,
      menuButtonTopRpx: (menuButton.top - systemInfo.statusBarHeight) * 2,
      menuButtonHeightRpx: menuButton.height * 2
    })
  },

  // 返回按钮点击
  onBackClick() {
    wx.navigateBack({
      fail: () => {
        wx.switchTab({
          url: '/pages/index/index'
        })
      }
    })
  },

  // 选择文件
  onSelectFile() {
    wx.chooseMessageFile({
      count: 9,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        // 处理所有选择的文件
        const files = res.tempFiles.map((file, index) => ({
          id: Date.now() + index,
          name: file.name,
          size: this.formatFileSize(file.size),
          path: file.path,
          originalSize: file.size,
          compressedSize: null
        }))

        if (files.length === 1) {
          // 单文件处理
          this.setData({
            selectedFiles: files,
            fileSelected: true,
            fileName: files[0].name,
            fileSize: files[0].size,
            filePath: files[0].path
          })
        } else {
          // 多文件处理
          this.setData({
            selectedFiles: files,
            fileSelected: true,
            fileName: `${files.length}个文件`,
            fileSize: this.getTotalSize(res.tempFiles)
          })
        }

        wx.showToast({
          title: `已选择${files.length}个文件`,
          icon: 'success'
        })
      },
      fail: (err) => {
        console.error('选择文件失败:', err)
        wx.showToast({
          title: '选择文件失败',
          icon: 'none'
        })
      }
    })
  },

  // 处理文件选择结果
  handleFileSelection(file) {
    this.setData({
      fileSelected: true,
      fileName: file.name,
      fileSize: this.formatFileSize(file.size),
      filePath: file.path
    })
  },

  // 底部选择PDF按钮
  onSelectPDF() {
    wx.showActionSheet({
      itemList: ['从文件选择', '从聊天记录选择'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 从文件选择
          this.onSelectFile()
        } else if (res.tapIndex === 1) {
          // 从聊天记录选择
          this.selectFromChat()
        }
      }
    })
  },

  // 从聊天记录选择
  selectFromChat() {
    wx.chooseMessageFile({
      count: 9,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        // 处理所有选择的文件
        const files = res.tempFiles.map((file, index) => ({
          id: Date.now() + index,
          name: file.name,
          size: this.formatFileSize(file.size),
          path: file.path,
          originalSize: file.size,
          compressedSize: null
        }))

        if (files.length === 1) {
          // 单文件处理
          this.setData({
            selectedFiles: files,
            fileSelected: true,
            fileName: files[0].name,
            fileSize: files[0].size,
            filePath: files[0].path
          })
        } else {
          // 多文件处理
          this.setData({
            selectedFiles: files,
            fileSelected: true,
            fileName: `${files.length}个文件`,
            fileSize: this.getTotalSize(res.tempFiles)
          })
        }

        wx.showToast({
          title: `已选择${files.length}个文件`,
          icon: 'success'
        })

        // 如果只选择了一个文件，保持原有的单文件显示逻辑
        if (files.length === 1) {
          this.handleFileSelection(res.tempFiles[0])
        }
      },
      fail: (err) => {
        console.error('选择文件失败:', err)
        wx.showToast({
          title: '选择文件失败',
          icon: 'none'
        })
      }
    })
  },



  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 计算总文件大小
  getTotalSize(files) {
    const totalBytes = files.reduce((sum, file) => sum + file.size, 0)
    return this.formatFileSize(totalBytes)
  },



  // 页面范围变化
  onPageRangeChange(e) {
    const range = e.currentTarget.dataset.range
    let rangeText = ''

    switch(range) {
      case 'all':
        rangeText = '全部页面'
        break
      case 'custom':
        rangeText = '自定义范围'
        break
    }

    this.setData({
      pageRange: range,
      pageRangeText: rangeText
    })
  },

  // 自定义页面范围输入
  onCustomPageRangeInput(e) {
    this.setData({
      customPageRange: e.detail.value
    })
  },

  // 移除文件
  onRemoveFile(e) {
    const fileId = e.currentTarget.dataset.id
    const selectedFiles = this.data.selectedFiles.filter(file => file.id !== fileId)

    this.setData({
      selectedFiles: selectedFiles
    })

    // 如果没有文件了，重置状态
    if (selectedFiles.length === 0) {
      this.setData({
        fileSelected: false,
        fileName: '',
        fileSize: '',
        filePath: ''
      })
    } else if (selectedFiles.length === 1) {
      // 如果只剩一个文件，更新单文件显示信息
      this.setData({
        fileName: selectedFiles[0].name,
        fileSize: selectedFiles[0].size,
        filePath: selectedFiles[0].path
      })
    }

    wx.showToast({
      title: '文件已移除',
      icon: 'success'
    })
  },

  // 设置按钮点击
  onSettings() {
    // 暂时不需要设置功能
    wx.showToast({
      title: '暂无设置选项',
      icon: 'none'
    })
  },

  // 开始转换
  onConvertToImages() {
    if (!this.validateSettings()) {
      return
    }

    this.setData({
      converting: true,
      progressPercent: 0
    })

    // 模拟转换进度
    this.simulateConversion()
  },

  // 验证设置
  validateSettings() {
    // 验证自定义页面范围
    if (this.data.pageRange === 'custom' && !this.data.customPageRange.trim()) {
      wx.showToast({
        title: '请输入页面范围',
        icon: 'none'
      })
      return false
    }

    return true
  },

  // 模拟转换过程
  simulateConversion() {
    let progress = 0
    const timer = setInterval(() => {
      progress += Math.random() * 15
      if (progress >= 100) {
        progress = 100
        clearInterval(timer)
        this.onConversionComplete()
      }
      this.setData({
        progressPercent: Math.floor(progress)
      })
    }, 300)
  },

  // 转换完成
  onConversionComplete() {
    // 模拟生成的图片数量
    let imageCount = 10 // 默认10页

    if (this.data.pageRange === 'custom' && this.data.customPageRange.trim()) {
      // 这里可以解析自定义页面范围，暂时使用默认值
      imageCount = 5 // 模拟自定义范围的图片数量
    }
    
    const convertedImages = []
    for (let i = 0; i < imageCount; i++) {
      convertedImages.push({
        url: '/images/pdf-icon.svg', // 这里应该是实际转换后的图片URL，暂时使用PDF图标作为示例
        name: `page_${i + 1}.${this.data.selectedFormat}`
      })
    }

    this.setData({
      converting: false,
      converted: true,
      convertedImages: convertedImages,
      convertedImageCount: imageCount
    })

    wx.showToast({
      title: '转换完成！',
      icon: 'success'
    })
  },

  // 预览图片
  previewImage(e) {
    const index = e.currentTarget.dataset.index
    const urls = this.data.convertedImages.map(img => img.url)
    
    wx.previewImage({
      current: urls[index],
      urls: urls
    })
  },

  // 下载图片
  onDownloadImages() {
    if (this.data.convertedImages.length === 0) {
      wx.showToast({
        title: '没有可下载的图片',
        icon: 'none'
      })
      return
    }

    wx.showToast({
      title: '开始下载图片...',
      icon: 'loading'
    })

    // 这里应该实现实际的下载逻辑
    setTimeout(() => {
      wx.showToast({
        title: '图片已保存到相册',
        icon: 'success'
      })
    }, 2000)
  },

  // 转换其他文件
  onConvertAnother() {
    this.setData({
      fileSelected: false,
      converting: false,
      converted: false,
      fileName: '',
      fileSize: '',
      filePath: '',
      convertedImages: [],
      convertedImageCount: 0,
      progressPercent: 0,
      selectedFormat: 'png',
      imageQuality: 90,
      pageRange: 'all',
      pageRangeText: '全部页面',
      customPageRange: ''
    })
  },

  // 开始转换（从底部胶囊按钮触发）
  onStartConversion() {
    if (!this.data.fileSelected) {
      this.onSelectPDF()
      return
    }

    if (this.data.converted) {
      this.onDownloadImages()
      return
    }

    this.onConvertToImages()
  }
})
