# PDF解密页面

## 功能说明
这是一个PDF解密页面，参考compress页面的UI设计一比一抄袭制作。

## 主要功能
1. **文件选择**: 支持选择加密的PDF文件
2. **密码输入**: 弹窗式密码输入界面，支持密码显示/隐藏切换
3. **解锁进度**: 模拟解锁进度显示
4. **解锁结果**: 显示解锁成功状态和操作按钮
5. **多文件支持**: 支持同时选择多个PDF文件进行解锁

## UI设计特点
- 采用新拟物设计风格，与compress页面保持一致
- 自定义导航栏
- 密码输入弹窗带有毛玻璃效果
- 进度条动画效果
- 底部胶囊按钮设计

## 页面结构
- `pdf-unlock.wxml`: 页面结构文件
- `pdf-unlock.wxss`: 页面样式文件
- `pdf-unlock.js`: 页面逻辑文件
- `pdf-unlock.json`: 页面配置文件

## 使用的图标
- `/images/unlock.svg`: 解锁图标
- `/images/back.svg`: 返回图标
- `/images/pdf-icon.svg`: PDF文件图标

## 颜色主题
- 主色调: 橙色系 (#FF8C00, #FFA500)
- 成功色: 绿色系 (#27ae60, #2ecc71)
- 背景色: 浅灰色系 (#f8f9fa, #f0f2f5)

## 注意事项
- 目前为演示版本，实际的PDF解锁功能需要后端支持
- 密码验证和文件解锁逻辑为模拟实现
- 下载功能暂未实现，显示"开发中"提示
