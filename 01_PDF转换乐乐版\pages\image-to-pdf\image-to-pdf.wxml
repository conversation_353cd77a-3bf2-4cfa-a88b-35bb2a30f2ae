<!--pages/image-to-pdf/image-to-pdf.wxml-->
<!-- 自定义导航栏 -->
<view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
  <view class="navbar-content">
    <view class="navbar-back" bindtap="onBackClick" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx;">
      <image class="navbar-back-icon" src="/images/back.svg"></image>
    </view>
    <view class="navbar-title" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx; line-height: {{menuButtonHeightRpx}}rpx;">图片转PDF</view>
  </view>
</view>

<!-- 页面内容 -->
<view class="container" style="padding-top: {{navbarHeight}}px;">
  
  <!-- 文件上传区域 -->
  <view class="upload-section" wx:if="{{!hasImages}}">
    <view class="upload-area" bindtap="onSelectImages">
      <view class="upload-icon">
        <image class="upload-svg" src="/images/image-to-pdf.svg"></image>
      </view>
      <view class="upload-content">
        <view class="upload-title">选择图片文件</view>
        <view class="upload-subtitle">支持从相册、拍照或聊天记录选择</view>
        <view class="upload-tips">支持JPG、PNG、GIF等格式，最多9张图片</view>
      </view>
    </view>
  </view>

  <!-- 图片列表区域 -->
  <view class="images-section" wx:if="{{hasImages && !converting}}">
    <view class="section-header">
      <text class="section-title">已选择 {{selectedImages.length}} 张图片</text>
      <text class="add-more-btn" bindtap="onAddMoreImages">添加更多</text>
    </view>
    
    <!-- 图片列表 -->
    <view class="images-list">
      <view class="image-item" wx:for="{{selectedImages}}" wx:key="id">
        <view class="image-preview">
          <image class="preview-img" src="{{item.path}}" mode="aspectFill"></image>
          <view class="image-index">{{index + 1}}</view>
        </view>
        <view class="image-info">
          <view class="image-name">{{item.name}}</view>
          <view class="image-size">{{item.size}}</view>
        </view>
        <view class="image-actions">
          <view class="action-btn move-up" bindtap="onMoveUp" data-index="{{index}}" wx:if="{{index > 0}}">
            <text>↑</text>
          </view>
          <view class="action-btn move-down" bindtap="onMoveDown" data-index="{{index}}" wx:if="{{index < selectedImages.length - 1}}">
            <text>↓</text>
          </view>
          <view class="action-btn remove" bindtap="onRemoveImage" data-index="{{index}}">
            <text>×</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 设置选项区域 -->
  <view class="settings-section" wx:if="{{hasImages && !converting}}">
    <!-- 页面适配设置 -->
    <view class="setting-group">
      <view class="setting-header">
        <text class="setting-label">页面适配</text>
        <text class="setting-value">{{fitOptionText}}</text>
      </view>
      <view class="setting-options">
        <view class="option-item {{fitOption === 'fitDocumentToImage' ? 'active' : ''}}" 
              bindtap="onFitOptionChange" data-option="fitDocumentToImage">
          <text class="option-text">文档适配图片</text>
          <text class="option-desc">PDF页面大小适配图片尺寸</text>
        </view>
        <view class="option-item {{fitOption === 'fitImageToDocument' ? 'active' : ''}}" 
              bindtap="onFitOptionChange" data-option="fitImageToDocument">
          <text class="option-text">图片适配文档</text>
          <text class="option-desc">图片缩放适配标准页面</text>
        </view>
        <view class="option-item {{fitOption === 'maintainAspectRatio' ? 'active' : ''}}" 
              bindtap="onFitOptionChange" data-option="maintainAspectRatio">
          <text class="option-text">保持宽高比</text>
          <text class="option-desc">保持图片原始比例</text>
        </view>
      </view>
    </view>

    <!-- 颜色类型设置 -->
    <view class="setting-group">
      <view class="setting-header">
        <text class="setting-label">颜色类型</text>
        <text class="setting-value">{{colorTypeText}}</text>
      </view>
      <view class="setting-options horizontal">
        <view class="option-item {{colorType === 'color' ? 'active' : ''}}" 
              bindtap="onColorTypeChange" data-type="color">
          <text class="option-text">彩色</text>
        </view>
        <view class="option-item {{colorType === 'greyscale' ? 'active' : ''}}" 
              bindtap="onColorTypeChange" data-type="greyscale">
          <text class="option-text">灰度</text>
        </view>
        <view class="option-item {{colorType === 'blackwhite' ? 'active' : ''}}" 
              bindtap="onColorTypeChange" data-type="blackwhite">
          <text class="option-text">黑白</text>
        </view>
      </view>
    </view>

    <!-- 自动旋转设置 -->
    <view class="setting-group">
      <view class="setting-item">
        <text class="setting-label">自动旋转</text>
        <switch class="setting-switch" checked="{{autoRotate}}" bindchange="onAutoRotateChange"></switch>
      </view>
      <view class="setting-desc">自动旋转图片以适应页面方向</view>
    </view>
  </view>

  <!-- 转换进行中 -->
  <view class="converting-section" wx:if="{{converting}}">
    <view class="progress-card">
      <view class="progress-icon">
        <image class="progress-svg" src="/images/image-to-pdf.svg"></image>
      </view>
      <view class="progress-content">
        <view class="progress-title">正在转换为PDF...</view>
        <view class="progress-subtitle">正在处理 {{selectedImages.length}} 张图片</view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercent}}%"></view>
        </view>
        <view class="progress-text">{{progressPercent}}%</view>
      </view>
    </view>
  </view>

  <!-- 转换完成 -->
  <view class="result-section" wx:if="{{converted}}">
    <view class="result-card">
      <view class="result-icon">
        <image class="result-svg" src="/images/success.svg"></image>
      </view>
      <view class="result-content">
        <view class="result-title">转换完成！</view>
        <view class="result-subtitle">成功将 {{selectedImages.length}} 张图片转换为PDF</view>
        
        <!-- PDF预览 -->
        <view class="pdf-preview" wx:if="{{pdfPreviewUrl}}">
          <view class="preview-header">
            <text class="preview-title">PDF预览</text>
            <text class="preview-info">{{pdfFileName}} ({{pdfFileSize}})</text>
          </view>
          <view class="preview-container">
            <image class="pdf-preview-img" src="{{pdfPreviewUrl}}" mode="aspectFit"></image>
          </view>
        </view>

        <view class="result-actions">
          <view class="action-btn secondary" bindtap="onConvertAnother">
            <text>转换其他图片</text>
          </view>
          <view class="action-btn primary" bindtap="onDownloadPDF">
            <text>下载PDF</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部胶囊按钮 -->
  <view class="bottom-capsule" wx:if="{{!converting}}">
    <view class="capsule-btn left" bindtap="onSelectImages">
      <view class="capsule-text">{{hasImages ? '重新选择' : '选择图片'}}</view>
    </view>
    <view class="capsule-btn right {{!hasImages ? 'disabled' : ''}}" bindtap="onStartConversion" wx:if="{{!converted}}">
      <view class="capsule-text">开始转换</view>
    </view>
    <view class="capsule-btn right" bindtap="onDownloadPDF" wx:if="{{converted}}">
      <view class="capsule-text">下载PDF</view>
    </view>
  </view>

</view>
