/* pages/compress/compress.wxss */

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  position: relative;
}

.navbar-back {
  position: absolute;
  left: 32rpx;
  width: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.navbar-back:active {
  opacity: 0.6;
  transform: scale(0.9);
}

.navbar-back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  position: absolute;
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 32rpx;
  margin-top: 30rpx;
}

/* 文件上传区域 */
.upload-section {
  margin-bottom: 48rpx;
}

.upload-area {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 80rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 4rpx dashed rgba(163, 177, 198, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* 新拟物设计阴影 */
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.upload-area:active {
  transform: scale(0.98);
  box-shadow:
    inset 6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    inset -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.upload-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(220, 20, 60, 0.3),
    0 0 8rpx rgba(220, 20, 60, 0.2);
}

.upload-svg {
  width: 80rpx;
  height: 80rpx;
  opacity: 0.8;
}

.upload-content {
  flex: 1;
}

.upload-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.upload-subtitle {
  font-size: 28rpx;
  color: #5a6c7d;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.upload-tips {
  font-size: 24rpx;
  color: #8a9aa9;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.upload-note {
  font-size: 24rpx;
  color: #999999;
  margin-top: 16rpx;
}

.web-link {
  color: #007AFF;
  text-decoration: underline;
}

/* 文件信息区域 */
.file-section {
  margin-bottom: 48rpx;
}

.file-info-card {
  background: #f0f2f5;
  border-radius: 24rpx;
  padding: 30rpx 52rpx;
  margin: 0 -32rpx 32rpx -32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow:
    8rpx 8rpx 16rpx rgba(163, 177, 198, 0.4),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
}

.file-icon {
  width: 100rpx;
  height: 100rpx;
  flex-shrink: 0;
  padding: 12rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(163, 177, 198, 0.3);
}

.file-details {
  flex: 1;
}

.file-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.file-size {
  font-size: 26rpx;
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.file-size-info {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  flex-wrap: wrap;
}

.size-label {
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.size-separator {
  margin: 0 16rpx;
}

.size-value {
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.size-value.compressed {
  color: #27ae60;
}

.size-value.placeholder {
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.remove-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  text-align: center;
  background: rgba(220, 20, 60, 0.1);
  color: #DC143C;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.3),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.remove-btn:active {
  transform: scale(0.95);
  box-shadow:
    inset 4rpx 4rpx 8rpx rgba(163, 177, 198, 0.4),
    inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.9);
}

/* 压缩选项 */
.compress-options {
  background: #f0f2f5;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow:
    8rpx 8rpx 16rpx rgba(163, 177, 198, 0.4),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
}

.options-header {
  margin-bottom: 32rpx;
}

.options-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.options-subtitle {
  font-size: 24rpx;
  color: #8a9aa9;
  margin-top: 8rpx;
}

.quality-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.quality-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-radius: 16rpx;
  background: #f0f2f5;
  transition: all 0.3s ease;
  box-shadow:
    4rpx 4rpx 8rpx rgba(163, 177, 198, 0.4),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
}

.quality-item.active {
  background: linear-gradient(135deg, #DC143C 0%, #FF6B6B 100%);
  color: white;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 0.2),
    inset -2rpx -2rpx 4rpx rgba(0, 0, 0, 0.2),
    0 0 16rpx rgba(220, 20, 60, 0.3);
}

.quality-item:active {
  transform: scale(0.98);
}

.quality-info {
  flex: 1;
}

.quality-name {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.quality-desc {
  font-size: 24rpx;
  opacity: 0.8;
}

.quality-ratio {
  font-size: 24rpx;
  font-weight: 500;
  opacity: 0.9;
}

/* 压缩按钮 */
.compress-action {
  display: flex;
  justify-content: center;
}

.compress-btn {
  background: linear-gradient(135deg, #DC143C 0%, #FF6B6B 100%);
  color: white;
  padding: 24rpx 80rpx;
  border-radius: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow:
    8rpx 8rpx 16rpx rgba(220, 20, 60, 0.3),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
}

.compress-btn:active {
  transform: scale(0.95);
  box-shadow:
    inset 4rpx 4rpx 8rpx rgba(0, 0, 0, 0.2),
    inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.2);
}

/* 压缩进行中 */
.compressing-section {
  margin-bottom: 48rpx;
}

.progress-card {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.progress-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(220, 20, 60, 0.3),
    0 0 8rpx rgba(220, 20, 60, 0.2);
}

.progress-svg {
  width: 80rpx;
  height: 80rpx;
  opacity: 0.8;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.progress-content {
  width: 100%;
}

.progress-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.progress-subtitle {
  font-size: 26rpx;
  color: #5a6c7d;
  margin-bottom: 40rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: rgba(163, 177, 198, 0.3);
  border-radius: 6rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.4),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #DC143C 0%, #FF6B6B 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
  box-shadow: 0 0 8rpx rgba(220, 20, 60, 0.3);
}

.progress-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #DC143C;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 压缩结果 */
.result-section {
  margin-bottom: 48rpx;
}

.result-card {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.result-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.result-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  padding: 16rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(50, 205, 50, 0.3),
    0 0 8rpx rgba(50, 205, 50, 0.2);
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #27ae60;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.result-comparison {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 16rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 0.8),
    inset -2rpx -2rpx 4rpx rgba(163, 177, 198, 0.2);
}

.size-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.size-label {
  font-size: 24rpx;
  color: #5a6c7d;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}



.size-arrow {
  font-size: 32rpx;
  color: #DC143C;
  font-weight: bold;
  margin: 0 16rpx;
}

.compression-stats {
  display: flex;
  justify-content: space-around;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 0.8),
    inset -2rpx -2rpx 4rpx rgba(163, 177, 198, 0.2);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 24rpx;
  color: #5a6c7d;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #27ae60;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 结果操作按钮 */
.result-actions {
  display: flex;
  gap: 24rpx;
  justify-content: center;
}

.action-btn.secondary {
  background: #f0f2f5;
  color: #5a6c7d;
  padding: 20rpx 40rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow:
    6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.action-btn.primary {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow:
    6rpx 6rpx 12rpx rgba(39, 174, 96, 0.3),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
}

/* 使用提示 */
.tips-section {
  margin-top: 48rpx;
}

.tips-card {
  background: #f0f2f5;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow:
    8rpx 8rpx 16rpx rgba(163, 177, 198, 0.4),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.tips-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  opacity: 0.8;
}

.tips-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
}

.tip-text {
  font-size: 26rpx;
  color: #5a6c7d;
  line-height: 1.5;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

/* 响应式设计 */
@media (max-width: 400px) {
  .result-actions {
    flex-direction: column;
    align-items: center;
  }

  .action-btn.secondary,
  .action-btn.primary {
    width: 100%;
    text-align: center;
  }

  .result-comparison {
    flex-direction: column;
    gap: 16rpx;
  }

  .size-arrow {
    transform: rotate(90deg);
    margin: 8rpx 0;
  }
}

/* 高级选项样式 */
.advanced-options {
  margin-top: 32rpx;
  padding-top: 32rpx;
  border-top: 1rpx solid rgba(163, 177, 198, 0.3);
}

.advanced-header {
  margin-bottom: 24rpx;
}

.advanced-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(163, 177, 198, 0.2);
}

.option-item:last-child {
  border-bottom: none;
}

.option-info {
  flex: 1;
}

.option-name {
  font-size: 28rpx;
  color: #2c3e50;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.option-desc {
  font-size: 24rpx;
  color: #8a9aa9;
  line-height: 1.4;
}

.target-size {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.target-size input {
  width: 120rpx;
  height: 60rpx;
  border: 2rpx solid #e0e6ed;
  border-radius: 8rpx;
  text-align: center;
  font-size: 26rpx;
  background: white;
}

.target-size text {
  font-size: 24rpx;
  color: #8a9aa9;
}

/* 预估效果样式 */
.estimate-section {
  margin-top: 32rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16rpx;
  border: 1rpx solid rgba(163, 177, 198, 0.3);
}

.estimate-header {
  margin-bottom: 20rpx;
}

.estimate-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #2c3e50;
}

.estimate-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.estimate-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.estimate-label {
  font-size: 24rpx;
  color: #8a9aa9;
}

.estimate-value {
  font-size: 24rpx;
  color: #2c3e50;
  font-weight: 500;
}

.estimate-value.success {
  color: #27ae60;
  font-weight: 600;
}

/* 提示图标样式 */
.tip-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.tip-icon {
  font-size: 32rpx;
  width: 40rpx;
  text-align: center;
}

/* 滑块选项样式 */
.slider-option {
  margin-bottom: 32rpx;
}

.slider-option:last-child {
  margin-bottom: 0;
}

.slider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.slider-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.slider-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #DC143C;
  background: rgba(255, 255, 255, 0.3);
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  min-width: 60rpx;
  text-align: center;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.3),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.slider-tips {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
  font-size: 24rpx;
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.tip-left, .tip-right {
  font-size: 24rpx;
  color: #5a6c7d;
}

/* 主要内容区域 */
.main-content {
  padding: 30rpx;
  padding-bottom: 260rpx; /* 为底部固定区域预留空间 */
}

/* 预估效果卡片 */
.estimate-card {
  background: #f0f2f5;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow:
    8rpx 8rpx 16rpx rgba(163, 177, 198, 0.4),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
}

/* 底部固定区域 */
.footer-area {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 80rpx;
  padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));
  z-index: 100;
}

/* 压缩设置区域 */
.compress-settings {
  background: rgba(240, 242, 245, 0.98);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.setting-item {
  margin-bottom: 15rpx;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.setting-label {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.setting-value {
  min-width: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #DC143C;
  font-weight: 500;
  padding: 4rpx 16rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.3);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.3),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.slider-container {
  padding: 30rpx 20rpx;
  border-radius: 20rpx;
  background: #f0f2f5;
  box-shadow:
    inset 6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    inset -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}



/* 底部胶囊按钮样式 */
.bottom-actions {
  position: fixed;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 36rpx;
  z-index: 1000;
}

.capsule-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 17rpx 48rpx;
  border-radius: 42rpx;
  box-shadow:
    0 6rpx 24rpx rgba(0, 0, 0, 0.12),
    0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  min-width: 240rpx;
  height: 55rpx;
}

.capsule-btn:active {
  transform: scale(0.96);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.16),
    0 1rpx 2rpx rgba(0, 0, 0, 0.12);
}

.capsule-btn.left {
  background: linear-gradient(135deg, #DC143C 0%, #FF6B6B 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(220, 20, 60, 0.3),
    0 2rpx 6rpx rgba(220, 20, 60, 0.2);
}

.capsule-btn.right {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(39, 174, 96, 0.3),
    0 2rpx 6rpx rgba(39, 174, 96, 0.2);
}

.capsule-btn.disabled {
  background: linear-gradient(135deg, #CCCCCC 0%, #999999 100%);
  color: rgba(255, 255, 255, 0.7);
  pointer-events: none;
  box-shadow:
    0 6rpx 24rpx rgba(0, 0, 0, 0.12),
    0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

/* 底部胶囊按钮容器 */
.bottom-capsule {
  position: fixed;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 24rpx;
  z-index: 1000;
}

.capsule-text {
  font-size: 39rpx;
  font-weight: 500;
  line-height: 1;
}



/* 自定义目标大小设置弹窗 */
.target-size-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(163, 177, 198, 0.3);
  backdrop-filter: blur(20rpx);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(20rpx);
  }
}

.modal-content {
  background: rgba(240, 242, 245, 0.95);
  backdrop-filter: blur(30rpx);
  border-radius: 32rpx;
  width: 100%;
  max-width: 600rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.4),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
  animation: slideUp 0.4s ease-out;
  overflow: hidden;
}

@keyframes slideUp {
  from {
    transform: translateY(80rpx) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.modal-header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  padding: 40rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  border-bottom: 1rpx solid rgba(163, 177, 198, 0.2);
  box-shadow:
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8),
    0 1rpx 3rpx rgba(163, 177, 198, 0.1);
}

.modal-title {
  color: #2c3e50;
  font-size: 32rpx;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow:
    4rpx 4rpx 8rpx rgba(163, 177, 198, 0.3),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.modal-close:active {
  transform: scale(0.95);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.3),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.close-icon {
  color: #5a6c7d;
  font-size: 28rpx;
  font-weight: bold;
}

.modal-body {
  padding: 48rpx 32rpx;
  background: rgba(255, 255, 255, 0.4);
}

.input-section {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.input-label {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.input-container {
  position: relative;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  box-shadow:
    inset 4rpx 4rpx 8rpx rgba(163, 177, 198, 0.2),
    inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.9);
}

.input-container:focus-within {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(220, 20, 60, 0.3);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.1),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 1),
    0 0 0 4rpx rgba(220, 20, 60, 0.1);
}

.size-input {
  width: 100%;
  padding: 28rpx 140rpx 28rpx 28rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #2c3e50;
  background: transparent;
  border: none;
  outline: none;
}

.input-unit {
  position: absolute;
  right: 28rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #5a6c7d;
  font-size: 26rpx;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.6);
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.2),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
  pointer-events: none;
}

.input-tip {
  font-size: 26rpx;
  color: #5a6c7d;
  line-height: 1.5;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.1),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.modal-footer {
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid rgba(163, 177, 198, 0.2);
  display: flex;
  gap: 24rpx;
  box-shadow:
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.modal-btn:active {
  transform: scale(0.96);
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.8);
  color: #5a6c7d;
  box-shadow:
    6rpx 6rpx 12rpx rgba(163, 177, 198, 0.3),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.cancel-btn:active {
  box-shadow:
    inset 3rpx 3rpx 6rpx rgba(163, 177, 198, 0.3),
    inset -3rpx -3rpx 6rpx rgba(255, 255, 255, 0.8);
}

.confirm-btn {
  background: rgba(220, 20, 60, 0.9);
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  box-shadow:
    6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2),
    0 0 20rpx rgba(220, 20, 60, 0.3);
}

.confirm-btn:active {
  box-shadow:
    inset 3rpx 3rpx 6rpx rgba(163, 177, 198, 0.4),
    inset -3rpx -3rpx 6rpx rgba(255, 255, 255, 0.8),
    0 0 15rpx rgba(220, 20, 60, 0.4);
}
