// pages/pdf-split/pdf-split.js
Page({
  data: {
    statusBarHeight: 0,
    navbarHeight: 0,
    menuButtonTopRpx: 0,
    menuButtonHeightRpx: 0,

    // 文件状态
    fileSelected: false,
    splitting: false,
    splitted: false,

    // 文件信息
    fileName: '',
    fileSize: '',
    filePath: '',
    totalPages: 0,

    // 拆分设置
    splitMode: '', // 'pages', 'range', 'single'
    splitModeText: '请选择拆分方式',
    pagesPerFile: 1,
    rangeStart: 1,
    rangeEnd: 1,

    // 弹窗状态
    showSplitModeModal: false,

    // 预估结果
    estimatedFiles: 0,
    rangePages: 0,

    // 拆分进度
    progressPercent: 0,

    // 拆分结果
    resultFiles: [],

    // 按钮状态
    canSplit: false
  },

  onLoad(options) {
    this.initNavbar()
    // 页面加载后直接选择PDF文件
    setTimeout(() => {
      this.onSelectPDF()
    }, 500)
  },

  // 初始化导航栏
  initNavbar() {
    const systemInfo = wx.getSystemInfoSync()
    const statusBarHeight = systemInfo.statusBarHeight
    const navbarHeight = statusBarHeight + 44

    const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
    const screenWidth = systemInfo.screenWidth
    const rpxRatio = 750 / screenWidth
    const menuButtonTopRpx = (menuButtonInfo.top - statusBarHeight + 1) * rpxRatio
    const menuButtonHeightRpx = menuButtonInfo.height * rpxRatio

    this.setData({
      statusBarHeight,
      navbarHeight,
      menuButtonTopRpx,
      menuButtonHeightRpx
    })
  },

  // 返回按钮
  onBackClick() {
    wx.navigateBack()
  },

  // 选择文件
  onSelectFile() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        const file = res.tempFiles[0]
        const fileName = file.name
        const fileSize = this.formatFileSize(file.size)

        // 模拟获取PDF页数（实际应用中需要调用PDF解析API）
        const totalPages = Math.floor(Math.random() * 50) + 10 // 10-60页随机

        this.setData({
          fileSelected: true,
          fileName,
          fileSize,
          filePath: file.path,
          totalPages,
          rangeEnd: totalPages,
          pagesPerFile: Math.min(5, totalPages),
          splitting: false,
          splitted: false
        })

        this.updateCanSplit()
      },
      fail: (err) => {
        console.error('选择文件失败:', err)
        wx.showToast({
          title: '选择文件失败',
          icon: 'none'
        })
      }
    })
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 移除文件
  onRemoveFile() {
    this.setData({
      fileSelected: false,
      fileName: '',
      fileSize: '',
      filePath: '',
      totalPages: 0,
      splitMode: '',
      splitModeText: '请选择拆分方式',
      canSplit: false
    })
  },

  // 显示拆分方式选择弹窗
  showSplitModeModal() {
    this.setData({
      showSplitModeModal: true
    })
  },

  // 隐藏拆分方式选择弹窗
  hideSplitModeModal() {
    this.setData({
      showSplitModeModal: false
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 选择拆分方式
  selectSplitMode(e) {
    const mode = e.currentTarget.dataset.mode
    this.setData({
      splitMode: mode
    })
  },

  // 确认拆分方式
  confirmSplitMode() {
    const { splitMode } = this.data
    if (!splitMode) {
      wx.showToast({
        title: '请选择拆分方式',
        icon: 'none'
      })
      return
    }

    let splitModeText = ''
    switch (splitMode) {
      case 'pages':
        splitModeText = '按页数拆分'
        break
      case 'range':
        splitModeText = '按页码范围拆分'
        break
      case 'single':
        splitModeText = '拆分为单页'
        break
    }

    this.setData({
      splitModeText,
      showSplitModeModal: false
    })

    this.updateEstimate()
    this.updateCanSplit()
  },

  // 更新预估结果
  updateEstimate() {
    const { splitMode, totalPages, pagesPerFile, rangeStart, rangeEnd } = this.data
    let estimatedFiles = 0
    let rangePages = 0

    switch (splitMode) {
      case 'pages':
        estimatedFiles = Math.ceil(totalPages / pagesPerFile)
        break
      case 'range':
        rangePages = Math.max(0, rangeEnd - rangeStart + 1)
        estimatedFiles = rangePages > 0 ? 1 : 0
        break
      case 'single':
        estimatedFiles = totalPages
        break
    }

    this.setData({
      estimatedFiles,
      rangePages
    })
  },

  // 更新是否可以拆分
  updateCanSplit() {
    const { fileSelected, splitMode, rangeStart, rangeEnd, totalPages } = this.data
    let canSplit = fileSelected && splitMode

    if (splitMode === 'range') {
      canSplit = canSplit && rangeStart >= 1 && rangeEnd <= totalPages && rangeStart <= rangeEnd
    }

    this.setData({
      canSplit
    })
  },

  // 每个文件页数变化
  onPagesPerFileChange(e) {
    const pagesPerFile = parseInt(e.detail.value)
    this.setData({
      pagesPerFile
    })
    this.updateEstimate()
  },

  // 起始页输入
  onRangeStartInput(e) {
    const rangeStart = parseInt(e.detail.value) || 1
    this.setData({
      rangeStart: Math.max(1, Math.min(rangeStart, this.data.totalPages))
    })
    this.updateEstimate()
    this.updateCanSplit()
  },

  // 结束页输入
  onRangeEndInput(e) {
    const rangeEnd = parseInt(e.detail.value) || this.data.totalPages
    this.setData({
      rangeEnd: Math.max(1, Math.min(rangeEnd, this.data.totalPages))
    })
    this.updateEstimate()
    this.updateCanSplit()
  },

  // 选择PDF（底部按钮）
  onSelectPDF() {
    this.onSelectFile()
  },

  // 开始拆分
  onStartSplit() {
    if (!this.data.canSplit) {
      wx.showToast({
        title: '请完成拆分设置',
        icon: 'none'
      })
      return
    }

    this.setData({
      splitting: true,
      progressPercent: 0
    })

    // 模拟拆分进度
    this.simulateSplitProgress()
  },

  // 模拟拆分进度
  simulateSplitProgress() {
    let progress = 0
    const timer = setInterval(() => {
      progress += Math.random() * 15 + 5
      if (progress >= 100) {
        progress = 100
        clearInterval(timer)
        setTimeout(() => {
          this.completeSplit()
        }, 500)
      }
      this.setData({
        progressPercent: Math.floor(progress)
      })
    }, 200)
  },

  // 完成拆分
  completeSplit() {
    const { splitMode, totalPages, pagesPerFile, estimatedFiles, fileName } = this.data

    // 生成结果文件列表
    const resultFiles = []
    const baseName = fileName.replace('.pdf', '')

    for (let i = 0; i < estimatedFiles; i++) {
      let pages = ''
      let name = ''

      switch (splitMode) {
        case 'pages':
          const startPage = i * pagesPerFile + 1
          const endPage = Math.min((i + 1) * pagesPerFile, totalPages)
          pages = endPage === startPage ? `${startPage}页` : `${endPage - startPage + 1}页`
          name = `${baseName}_第${i + 1}部分.pdf`
          break
        case 'range':
          pages = `${this.data.rangePages}页`
          name = `${baseName}_页码${this.data.rangeStart}-${this.data.rangeEnd}.pdf`
          break
        case 'single':
          pages = '1页'
          name = `${baseName}_第${i + 1}页.pdf`
          break
      }

      resultFiles.push({
        id: i + 1,
        name,
        pages,
        path: `/temp/split_${i + 1}.pdf` // 模拟路径
      })
    }

    this.setData({
      splitting: false,
      splitted: true,
      resultFiles
    })

    wx.showToast({
      title: '拆分完成！',
      icon: 'success'
    })
  },

  // 下载单个文件
  onDownloadSingleFile(e) {
    const index = e.currentTarget.dataset.index
    const file = this.data.resultFiles[index]

    wx.showToast({
      title: `下载 ${file.name}`,
      icon: 'success'
    })

    // 这里应该实现实际的文件下载逻辑
    console.log('下载文件:', file)
  },

  // 下载全部文件
  onDownloadAll() {
    wx.showToast({
      title: '下载全部文件',
      icon: 'success'
    })

    // 这里应该实现实际的批量下载逻辑
    console.log('下载全部文件:', this.data.resultFiles)
  },

  // 拆分其他文件
  onSplitAnother() {
    this.setData({
      fileSelected: false,
      splitting: false,
      splitted: false,
      fileName: '',
      fileSize: '',
      filePath: '',
      totalPages: 0,
      splitMode: '',
      splitModeText: '请选择拆分方式',
      resultFiles: [],
      canSplit: false
    })
  }
})