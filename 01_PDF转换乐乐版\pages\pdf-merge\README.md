# PDF文件合并功能

## 功能概述
PDF文件合并功能允许用户选择多个PDF文件，按照指定顺序合并成一个PDF文件。

## UI设计特点
参考了compress页面的UI设计风格，采用新拟物化设计（Neumorphism）：
- 柔和的阴影效果
- 渐变背景
- 圆角设计
- 按压反馈动画

## 主要功能

### 1. 文件选择
- 支持选择多个PDF文件
- 显示文件名和大小
- 可以添加更多文件

### 2. 文件管理
- 显示文件顺序编号
- 支持上下移动文件顺序
- 支持删除单个文件
- 自定义合并后的文件名

### 3. 合并过程
- 实时进度显示
- 旋转动画效果
- 进度条动画

### 4. 结果展示
- 显示合并结果
- 文件信息展示
- 下载和重新合并选项

## 文件结构
```
pages/pdf-merge/
├── pdf-merge.js      # 页面逻辑
├── pdf-merge.wxml    # 页面结构
├── pdf-merge.wxss    # 页面样式
├── pdf-merge.json    # 页面配置
└── README.md         # 说明文档
```

## 样式特色

### 颜色主题
- 主色调：紫色系 (#8A2BE2, #9370DB)
- 成功色：绿色系 (#27AE60, #2ECC71)
- 背景色：浅灰色系 (#F8F9FA, #F0F2F5)

### 交互效果
- 按钮按压缩放效果
- 阴影变化动画
- 进度条平滑过渡
- 旋转加载动画

## 技术实现

### 文件选择
使用 `wx.chooseMessageFile` API选择PDF文件，支持多选。

### 文件排序
通过数组操作实现文件顺序调整：
- `onMoveUp()` - 向上移动
- `onMoveDown()` - 向下移动

### 进度模拟
使用定时器模拟合并进度，实际项目中需要对接真实的PDF合并API。

### 状态管理
- `selectedFiles` - 已选择的文件列表
- `merging` - 合并进行中状态
- `merged` - 合并完成状态

## 使用方法

1. 从首页点击"PDF文件合并"进入功能页面
2. 点击上传区域选择多个PDF文件
3. 调整文件顺序（可选）
4. 设置合并后的文件名
5. 点击"开始合并"按钮
6. 等待合并完成
7. 下载合并后的文件

## 注意事项

1. 至少需要选择2个PDF文件才能进行合并
2. 文件名不能为空
3. 当前为演示版本，实际合并功能需要对接后端API
4. 下载功能需要根据实际需求实现

## 后续优化建议

1. 添加文件预览功能
2. 支持拖拽排序
3. 添加合并选项（如页面范围选择）
4. 优化大文件处理性能
5. 添加合并历史记录
