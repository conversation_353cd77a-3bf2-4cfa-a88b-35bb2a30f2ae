# PDF转Excel页面

## 功能描述
将PDF文件转换为Excel格式，支持表格数据和文本内容的转换。

## 页面特性

### 设计风格
- 参考compress和pdf-merge页面的设计风格
- 使用相同的导航栏、卡片布局和按钮样式
- 保持整体UI的一致性

### 主要功能
1. **文件选择**
   - 支持选择PDF文件
   - 显示文件信息（名称、大小）
   - 可移除已选择的文件

2. **转换设置**
   - **转换模式**：
     - 表格模式：适合包含表格数据的PDF
     - 文本模式：适合纯文本内容的PDF
   - **页面范围**：
     - 全部页面：转换整个PDF文件
     - 自定义：指定特定页面范围（如：1-5,8,10-12）

3. **转换过程**
   - 实时进度显示
   - 转换状态反馈
   - 动画效果增强用户体验

4. **结果展示**
   - 转换完成提示
   - 显示转换后的文件信息
   - 提供下载和重新转换选项

### 交互设计
- **底部胶囊按钮**：选择PDF / 开始转换
- **卡片式布局**：清晰展示文件信息和设置选项
- **模态式设置**：直观的转换模式和页面范围选择
- **进度反馈**：转换过程中的实时进度条

### 技术实现
- 使用微信小程序原生组件
- 自定义导航栏适配不同设备
- 响应式布局设计
- 平滑的动画过渡效果

## 文件结构
```
pdf-to-excel/
├── pdf-to-excel.wxml    # 页面结构
├── pdf-to-excel.wxss    # 页面样式
├── pdf-to-excel.js      # 页面逻辑
├── pdf-to-excel.json    # 页面配置
└── README.md           # 说明文档
```

## 使用的图标
- `/images/excel-icon.svg` - Excel图标（上传区域）
- `/images/pdf-icon.svg` - PDF文件图标
- `/images/success.svg` - 成功完成图标
- `/images/back.svg` - 返回按钮图标
