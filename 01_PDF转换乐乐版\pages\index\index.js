// pages/index/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0,
    navbarHeight: 0,
    menuButtonWidth: 0
  },

  // 热门功能点击事件
  onCompressClick() {
    wx.navigateTo({
      url: '/pages/compress/compress'
    })
  },

  onConvertClick() {
    wx.navigateTo({
      url: '/pages/pdf-to-word/pdf-to-word'
    })
  },

  onUnlockClick() {
    wx.navigateTo({
      url: '/pages/pdf-unlock/pdf-unlock'
    })
  },

  onImageToPdfClick() {
    wx.navigateTo({
      url: '/pages/image-to-pdf/image-to-pdf'
    })
  },

  // 搜索点击事件
  onSearchClick() {
    wx.showModal({
      title: '搜索功能',
      content: '搜索功能开发中，敬请期待！',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 全部功能点击事件
  onMergeClick() {
    wx.navigateTo({
      url: '/pages/pdf-merge/pdf-merge'
    })
  },



  onExcelClick() {
    wx.navigateTo({
      url: '/pages/pdf-to-excel/pdf-to-excel'
    })
  },

  onPptClick() {
    wx.navigateTo({
      url: '/pages/pdf-to-ppt/pdf-to-ppt'
    })
  },



  onToImageClick() {
    wx.navigateTo({
      url: '/pages/pdf-to-image/pdf-to-image'
    })
  },

  onSplitClick() {
    wx.navigateTo({
      url: '/pages/pdf-split/pdf-split'
    })
  },


  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('页面加载完成')

    // 获取系统信息，设置导航栏高度
    const systemInfo = wx.getSystemInfoSync()
    const statusBarHeight = systemInfo.statusBarHeight
    const navbarHeight = statusBarHeight + 44 // 状态栏高度 + 导航栏高度

    // 获取胶囊按钮信息
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
    const menuButtonWidth = menuButtonInfo.width
    const menuButtonHeight = menuButtonInfo.height // 官方胶囊按钮高度，通常为32px
    const menuButtonTop = menuButtonInfo.top // 胶囊按钮距离顶部的距离

    // 将px转换为rpx (750rpx = 屏幕宽度px)
    const screenWidth = systemInfo.screenWidth
    const rpxRatio = 750 / screenWidth // rpx转换比例
    const menuButtonHeightRpx = menuButtonHeight * rpxRatio
    const searchBoxHeightRpx = (menuButtonHeight - 2) * rpxRatio // 搜索框比胶囊按钮小2px
    const menuButtonTopRpx = (menuButtonTop - statusBarHeight + 1) * rpxRatio // 胶囊按钮在导航栏中的top位置

    this.setData({
      statusBarHeight: statusBarHeight,
      navbarHeight: navbarHeight,
      menuButtonWidth: menuButtonWidth,
      menuButtonHeight: menuButtonHeight,
      menuButtonHeightRpx: menuButtonHeightRpx,
      searchBoxHeightRpx: searchBoxHeightRpx,
      menuButtonTopRpx: menuButtonTopRpx
    })
  },



  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('页面初次渲染完成')
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('页面显示')
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.log('页面隐藏')
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    console.log('页面卸载')
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('用户下拉刷新')
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('页面上拉触底')
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: 'PDF转换工具',
      path: '/pages/index/index'
    }
  }
})
