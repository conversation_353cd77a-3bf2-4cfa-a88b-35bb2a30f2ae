<!--pages/compress/compress.wxml-->
<!-- 自定义导航栏 -->
<view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
  <view class="navbar-content">
    <view class="navbar-back" bindtap="onBackClick" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx;">
      <image class="navbar-back-icon" src="/images/back.svg"></image>
    </view>
    <view class="navbar-title" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx; line-height: {{menuButtonHeightRpx}}rpx;">PDF智能压缩</view>
  </view>
</view>

<!-- 页面内容 -->
<view class="container" style="padding-top: {{navbarHeight}}px;">
  
  <!-- 文件上传区域 -->
  <view class="upload-section" wx:if="{{!fileSelected}}">
    <view class="upload-area" bindtap="onSelectFile">
      <view class="upload-icon">
        <image class="upload-svg" src="/images/compress.svg"></image>
      </view>
      <view class="upload-content">
        <view class="upload-title">选择PDF文件</view>
        <view class="upload-subtitle">点击选择或拖拽文件到此处</view>
        <view class="upload-tips">支持PDF格式</view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content" wx:if="{{fileSelected && !compressing && !compressed}}">
    <!-- 文件信息卡片 -->
    <view class="file-info-card">
      <image class="file-icon" src="/images/document.svg"></image>
      <view class="file-details">
        <view class="file-name">{{fileName}}</view>
        <view class="file-size">{{fileSize}}</view>
      </view>
      <view class="remove-btn" bindtap="onRemoveFile">移除</view>
    </view>

    <!-- 预估效果卡片 -->
    <view class="estimate-card" wx:if="{{showEstimate}}">
      <view class="estimate-header">
        <text class="estimate-title">预估效果</text>
      </view>
      <view class="estimate-content">
        <view class="estimate-item">
          <text class="estimate-label">原始大小:</text>
          <text class="estimate-value">{{originalSizeText}}</text>
        </view>
        <view class="estimate-item">
          <text class="estimate-label">预估大小:</text>
          <text class="estimate-value success">{{estimatedSize}}</text>
        </view>
        <view class="estimate-item">
          <text class="estimate-label">压缩率:</text>
          <text class="estimate-value success">{{estimatedRatio}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部固定区域 -->
  <view class="footer-area" wx:if="{{fileSelected && !compressing && !compressed}}">
    <!-- 压缩设置 -->
    <view class="compress-settings">
      <!-- 压缩强度滑块 -->
      <view class="setting-item">
        <view class="setting-header">
          <text class="setting-label">压缩强度</text>
          <text class="setting-value">{{compressionLevel}}</text>
        </view>
        <view class="slider-container">
          <slider
            value="{{compressionLevel}}"
            min="1"
            max="9"
            step="1"
            show-value="false"
            bindchange="onCompressionLevelChange"
            activeColor="#DC143C"
            backgroundColor="rgba(220, 20, 60, 0.1)"
            block-color="#ffffff"
            block-size="28"
          />
        </view>
      </view>
    </view>
  </view>

  <!-- 压缩进行中 -->
  <view class="compressing-section" wx:if="{{compressing}}">
    <view class="progress-card">
      <view class="progress-icon">
        <image class="progress-svg" src="/images/compress.svg"></image>
      </view>
      <view class="progress-content">
        <view class="progress-title">正在压缩PDF...</view>
        <view class="progress-subtitle">请稍候，正在为您优化文件大小</view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercent}}%"></view>
        </view>
        <view class="progress-text">{{progressPercent}}%</view>
      </view>
    </view>
  </view>

  <!-- 压缩完成 -->
  <view class="result-section" wx:if="{{compressed}}">
    <view class="result-card">
      <view class="result-icon">
        <image class="result-svg" src="/images/success.svg"></image>
      </view>
      <view class="result-content">
        <view class="result-title">压缩完成！</view>
        <view class="result-subtitle">文件已成功压缩</view>
        
        <view class="result-stats">
          <view class="stat-item">
            <view class="stat-label">原始大小</view>
            <view class="stat-value">{{originalSize}}</view>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <view class="stat-label">压缩后</view>
            <view class="stat-value success">{{compressedSize}}</view>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <view class="stat-label">压缩率</view>
            <view class="stat-value success">{{compressionRatio}}%</view>
          </view>
        </view>
        
        <view class="result-actions">
          <view class="action-btn secondary" bindtap="onCompressAnother">
            <text>压缩其他文件</text>
          </view>
          <view class="action-btn primary" bindtap="onDownloadPDF">
            <text>下载压缩文件</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部胶囊按钮 -->
  <view class="bottom-capsule" wx:if="{{compressed}}">
    <view class="capsule-btn left" bindtap="onCompressAnother">
      <view class="capsule-text">选择PDF</view>
    </view>
    <view class="capsule-btn right {{!compressed ? 'disabled' : ''}}" bindtap="onDownloadPDF">
      <view class="capsule-text">下载PDF</view>
    </view>
  </view>

</view>
