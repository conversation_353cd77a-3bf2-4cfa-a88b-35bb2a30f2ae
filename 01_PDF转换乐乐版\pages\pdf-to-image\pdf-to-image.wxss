/* pages/pdf-to-image/pdf-to-image.wxss */

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  position: relative;
}

.navbar-back {
  position: absolute;
  left: 32rpx;
  width: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.navbar-back:active {
  opacity: 0.6;
  transform: scale(0.9);
}

.navbar-back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  position: absolute;
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 32rpx;
  margin-top: 30rpx;
}

/* 文件上传区域 */
.upload-section {
  margin-bottom: 48rpx;
}

.upload-area {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 80rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 4rpx dashed rgba(163, 177, 198, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* 新拟物设计阴影 */
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.upload-area:active {
  transform: scale(0.98);
  box-shadow:
    inset 6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    inset -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.upload-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  box-shadow:
    8rpx 8rpx 16rpx rgba(163, 177, 198, 0.4),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8);
}

.upload-svg {
  width: 64rpx;
  height: 64rpx;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.upload-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 24rpx;
}

.upload-tips {
  font-size: 24rpx;
  color: #999999;
  padding: 16rpx 32rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 24rpx;
  border: 1rpx solid rgba(163, 177, 198, 0.2);
}

/* 主要内容区域 */
.main-content {
  margin-bottom: 48rpx;
}

.file-info-card {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.15),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8);
}

.file-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size-info {
  font-size: 24rpx;
  color: #666666;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.size-label {
  color: #999999;
}

.size-value {
  color: #333333;
  font-weight: 500;
}

.size-separator {
  margin: 0 8rpx;
}

.remove-btn {
  padding: 16rpx 24rpx;
  background: #f0f2f5;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666666;
  flex-shrink: 0;
}

/* 底部设置区域 */
.footer-area {
  position: fixed;
  bottom: 160rpx;
  left: 32rpx;
  right: 32rpx;
  z-index: 100;
}

.convert-settings {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.15),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8);
}

.setting-item {
  margin-bottom: 32rpx;
  position: relative;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.setting-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.setting-value {
  font-size: 26rpx;
  color: #DC143C;
  font-weight: 600;
}

.setting-arrow {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 24rpx;
  color: #999999;
}

.slider-container {
  margin-top: 16rpx;
}

/* 页面范围选择 */
.page-range-options {
  display: flex;
  gap: 16rpx;
  margin-top: 16rpx;
}

.range-option {
  flex: 1;
  padding: 16rpx;
  background: #f0f2f5;
  border-radius: 16rpx;
  text-align: center;
  font-size: 24rpx;
  color: #666666;
  transition: all 0.3s ease;
}

.range-option.selected {
  background: #DC143C;
  color: #ffffff;
}

.custom-range {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-top: 16rpx;
}

.range-input {
  flex: 1;
  padding: 16rpx;
  background: #f0f2f5;
  border-radius: 16rpx;
  text-align: center;
  font-size: 24rpx;
}

.range-separator {
  font-size: 24rpx;
  color: #666666;
}

/* 转换按钮 */
.convert-btn {
  margin-top: 32rpx;
  padding: 32rpx;
  background: #DC143C;
  border-radius: 20rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  transition: all 0.3s ease;
  box-shadow:
    0 8rpx 24rpx rgba(220, 20, 60, 0.3),
    0 2rpx 8rpx rgba(220, 20, 60, 0.2);
}

.convert-btn:active {
  transform: scale(0.98);
  box-shadow:
    0 4rpx 12rpx rgba(220, 20, 60, 0.4),
    0 1rpx 4rpx rgba(220, 20, 60, 0.3);
}

/* 格式选择弹窗 */
.format-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

.modal-content {
  background: #ffffff;
  border-radius: 32rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f2f5;
  border-radius: 50%;
}

.close-icon {
  font-size: 24rpx;
  color: #666666;
}

.modal-body {
  padding: 32rpx;
}

.format-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.format-option {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.format-option.selected {
  background: rgba(220, 20, 60, 0.1);
  border-color: #DC143C;
}

.format-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
}

.format-info {
  flex: 1;
}

.format-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.format-desc {
  display: block;
  font-size: 24rpx;
  color: #666666;
}

.format-check {
  font-size: 32rpx;
  color: #DC143C;
  font-weight: bold;
}

.modal-footer {
  display: flex;
  padding: 0 32rpx 32rpx;
  gap: 16rpx;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 20rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
}

.cancel-btn {
  background: #f0f2f5;
  color: #666666;
}

.confirm-btn {
  background: #DC143C;
  color: #ffffff;
}

/* 转换进行中 */
.converting-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
}

.progress-card {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 64rpx 48rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.15),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8);
}

.progress-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(220, 20, 60, 0.1);
  border-radius: 32rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.progress-svg {
  width: 64rpx;
  height: 64rpx;
}

.progress-content {
  width: 100%;
}

.progress-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.progress-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 48rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: rgba(220, 20, 60, 0.1);
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #DC143C 0%, #FF6B6B 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #DC143C;
}

/* 转换完成 */
.result-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
}

.result-card {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 48rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.15),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8);
}

.result-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(34, 197, 94, 0.1);
  border-radius: 32rpx;
}

.result-svg {
  width: 64rpx;
  height: 64rpx;
}

.result-content {
  width: 100%;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.result-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 32rpx;
}

/* 图片预览 */
.image-preview {
  margin-bottom: 32rpx;
}

.preview-scroll {
  white-space: nowrap;
}

.preview-item {
  display: inline-block;
  margin-right: 16rpx;
  text-align: center;
  vertical-align: top;
}

.preview-image {
  width: 120rpx;
  height: 160rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e5e5e5;
  margin-bottom: 8rpx;
}

.preview-name {
  font-size: 20rpx;
  color: #666666;
  display: block;
}

.result-actions {
  display: flex;
  gap: 16rpx;
  width: 100%;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 20rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: #f0f2f5;
  color: #666666;
}

.action-btn.primary {
  background: #DC143C;
  color: #ffffff;
}

.action-btn:active {
  transform: scale(0.98);
}

/* 底部胶囊按钮容器 */
.bottom-capsule {
  position: fixed;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 24rpx;
  z-index: 1000;
}

.capsule-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 17rpx 48rpx;
  border-radius: 42rpx;
  box-shadow:
    0 6rpx 24rpx rgba(0, 0, 0, 0.12),
    0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  min-width: 240rpx;
  height: 55rpx;
}

.capsule-btn:active {
  transform: scale(0.96);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.16),
    0 1rpx 2rpx rgba(0, 0, 0, 0.12);
}

.capsule-btn.left {
  background: linear-gradient(135deg, #FF69B4 0%, #FFB6C1 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(255, 105, 180, 0.3),
    0 2rpx 6rpx rgba(255, 105, 180, 0.2);
}

.capsule-btn.right {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(39, 174, 96, 0.3),
    0 2rpx 6rpx rgba(39, 174, 96, 0.2);
}

.capsule-btn.disabled {
  background: linear-gradient(135deg, #CCCCCC 0%, #999999 100%);
  color: rgba(255, 255, 255, 0.7);
  pointer-events: none;
  box-shadow:
    0 6rpx 24rpx rgba(0, 0, 0, 0.12),
    0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.capsule-text {
  font-size: 39rpx;
  font-weight: 500;
  line-height: 1;
}

/* 页面范围设置面板 */
.page-range-panel {
  position: fixed;
  bottom: 170rpx; /* 在底部按钮上方，增加间距 */
  left: 32rpx;
  right: 32rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow:
    0 8rpx 32rpx rgba(255, 105, 180, 0.15),
    0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  z-index: 999;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.panel-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

.panel-value {
  font-size: 28rpx;
  color: #FF69B4;
  font-weight: 500;
}

/* 页面范围选项 */
.range-options {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.range-item {
  flex: 1;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow:
    4rpx 4rpx 8rpx rgba(255, 105, 180, 0.1),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
}

.range-item.active {
  background: linear-gradient(135deg, #FF69B4 0%, #FFB6C1 100%);
  color: white;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 0.2),
    inset -2rpx -2rpx 4rpx rgba(0, 0, 0, 0.2),
    0 0 16rpx rgba(255, 105, 180, 0.3);
}

.range-item:active {
  transform: scale(0.98);
}

.range-text {
  font-size: 26rpx;
  font-weight: 500;
}

/* 自定义范围输入 */
.custom-range-input {
  margin-top: 16rpx;
}

.range-input {
  width: 100%;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  font-size: 26rpx;
  color: #2c3e50;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 105, 180, 0.1),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 105, 180, 0.2);
}
