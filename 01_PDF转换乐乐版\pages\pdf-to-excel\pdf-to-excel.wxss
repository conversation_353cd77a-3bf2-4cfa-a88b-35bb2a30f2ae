/* pages/pdf-to-excel/pdf-to-excel.wxss */

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  position: relative;
}

.navbar-back {
  position: absolute;
  left: 32rpx;
  width: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.navbar-back:active {
  opacity: 0.6;
  transform: scale(0.9);
}

.navbar-back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  position: absolute;
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 页面容器 */
.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 32rpx;
  margin-top: 30rpx;
}

/* 文件上传区域 */
.upload-section {
  margin-bottom: 48rpx;
}

.upload-area {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 80rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 4rpx dashed rgba(163, 177, 198, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 新拟物设计阴影 */
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.upload-area:active {
  transform: scale(0.98);
  box-shadow:
    inset 6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    inset -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.upload-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(39, 174, 96, 0.3),
    0 0 8rpx rgba(39, 174, 96, 0.2);
}

.upload-svg {
  width: 80rpx;
  height: 80rpx;
  opacity: 0.8;
}

.upload-content {
  flex: 1;
}

.upload-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.upload-subtitle {
  font-size: 28rpx;
  color: #5a6c7d;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.upload-tips {
  font-size: 24rpx;
  color: #8a9aa9;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

/* 主要内容区域 */
.main-content {
  padding: 0 40rpx;
}

.file-info-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 30rpx 52rpx;
  margin: 0 -40rpx 32rpx -40rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.file-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size-info {
  font-size: 24rpx;
  color: #666666;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.size-label {
  color: #999999;
}

.size-value {
  color: #333333;
  font-weight: 500;
}

.size-separator {
  margin: 0 16rpx;
}

.remove-btn {
  background: #27ae60;
  color: #ffffff;
  padding: 16rpx 24rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.remove-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 底部固定区域 */
.footer-area {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 80rpx;
  padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));
  z-index: 100;
}

/* 转换设置区域 */
.convert-settings {
  background: rgba(240, 242, 245, 0.98);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.setting-item {
  margin-bottom: 15rpx;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.setting-label {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.setting-value {
  min-width: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #27ae60;
  font-weight: 500;
  padding: 4rpx 16rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.3);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.3),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

/* 转换模式选择器 */
.mode-selector {
  display: flex;
  gap: 12rpx;
  padding: 20rpx;
  border-radius: 20rpx;
  background: #f0f2f5;
  box-shadow:
    inset 6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    inset -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.mode-btn {
  flex: 1;
  padding: 16rpx 12rpx;
  text-align: center;
  font-size: 24rpx;
  color: #666666;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  background: transparent;
}

.mode-btn.active {
  background: rgba(255, 255, 255, 0.8);
  color: #27ae60;
  font-weight: 600;
  box-shadow:
    2rpx 2rpx 4rpx rgba(163, 177, 198, 0.3),
    -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

/* 页面范围选择器 */
.range-selector {
  display: flex;
  gap: 12rpx;
  padding: 20rpx;
  border-radius: 20rpx;
  background: #f0f2f5;
  box-shadow:
    inset 6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    inset -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.range-btn {
  flex: 1;
  padding: 16rpx 12rpx;
  text-align: center;
  font-size: 24rpx;
  color: #666666;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  background: transparent;
}

.range-btn.active {
  background: rgba(255, 255, 255, 0.8);
  color: #27ae60;
  font-weight: 600;
  box-shadow:
    2rpx 2rpx 4rpx rgba(163, 177, 198, 0.3),
    -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.custom-range {
  margin-top: 16rpx;
}

.range-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #333333;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.3),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

/* 开始转换按钮 */
.convert-btn {
  margin-top: 20rpx;
  padding: 24rpx;
  text-align: center;
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  border-radius: 16rpx;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow:
    0 8rpx 16rpx rgba(39, 174, 96, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.convert-btn:active {
  transform: translateY(2rpx);
  box-shadow:
    0 4rpx 8rpx rgba(39, 174, 96, 0.3),
    inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
}

/* 转换进行中 */
.compressing-section {
  padding: 60rpx 40rpx;
}

.progress-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.progress-icon {
  margin-bottom: 32rpx;
}

.progress-svg {
  width: 120rpx;
  height: 120rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.progress-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.progress-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 40rpx;
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #66BB6A);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #4CAF50;
}

/* 结果区域 */
.result-section {
  padding: 60rpx 40rpx;
}

.result-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.result-icon {
  margin-bottom: 32rpx;
}

.result-svg {
  width: 120rpx;
  height: 120rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

.result-subtitle {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 40rpx;
}

.result-info {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 40rpx;
  text-align: left;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 28rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #666666;
  margin-right: 16rpx;
  min-width: 140rpx;
}

.info-value {
  color: #333333;
  font-weight: 500;
  flex: 1;
}

.result-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 16rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.98);
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #666666;
  border: 2rpx solid #e9ecef;
}

.action-btn.primary {
  background: #4CAF50;
  color: #ffffff;
}

/* 底部胶囊按钮容器 */
.bottom-capsule {
  position: fixed;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 24rpx;
  z-index: 1000;
}

.capsule-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 17rpx 48rpx;
  border-radius: 42rpx;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 240rpx;
  height: 55rpx;
}

.capsule-btn:active {
  transform: scale(0.96);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.16),
    0 1rpx 2rpx rgba(0, 0, 0, 0.12);
}

.capsule-btn.left {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(39, 174, 96, 0.3),
    0 2rpx 6rpx rgba(39, 174, 96, 0.2);
}

.capsule-btn.right {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(39, 174, 96, 0.3),
    0 2rpx 6rpx rgba(39, 174, 96, 0.2);
}

.capsule-btn.disabled {
  background: linear-gradient(135deg, #CCCCCC 0%, #999999 100%);
  color: rgba(255, 255, 255, 0.7);
  pointer-events: none;
  box-shadow:
    0 6rpx 24rpx rgba(0, 0, 0, 0.12),
    0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.capsule-text {
  font-size: 39rpx;
  font-weight: 500;
  line-height: 1;
}
