/* pages/pdf-to-ppt/pdf-to-ppt.wxss */

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  position: relative;
}

.navbar-back {
  position: absolute;
  left: 32rpx;
  width: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.navbar-back:active {
  opacity: 0.6;
  transform: scale(0.9);
}

.navbar-back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  position: absolute;
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 32rpx;
  margin-top: 30rpx;
}

/* 文件上传区域 - PPT主题色 */
.upload-section {
  margin-bottom: 48rpx;
}

.upload-area {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 80rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 4rpx dashed rgba(210, 71, 38, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* PPT主题色阴影 */
  box-shadow:
    12rpx 12rpx 24rpx rgba(210, 71, 38, 0.15),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.upload-area:active {
  transform: scale(0.98);
  box-shadow:
    inset 6rpx 6rpx 12rpx rgba(210, 71, 38, 0.2),
    inset -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.upload-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  box-shadow:
    8rpx 8rpx 16rpx rgba(210, 71, 38, 0.15),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8),
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 0.8),
    inset -2rpx -2rpx 4rpx rgba(210, 71, 38, 0.1);
}

.upload-svg {
  width: 64rpx;
  height: 64rpx;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.upload-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.upload-subtitle {
  font-size: 28rpx;
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.upload-tips {
  font-size: 24rpx;
  color: #D24726;
  background: rgba(210, 71, 38, 0.1);
  padding: 12rpx 24rpx;
  border-radius: 16rpx;
  text-shadow: none;
}

/* 主要内容区域 */
.main-content {
  margin-bottom: 48rpx;
}

/* 文件信息卡片 */
.file-info-card {
  background: #f0f2f5;
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 32rpx;
  box-shadow:
    8rpx 8rpx 16rpx rgba(210, 71, 38, 0.1),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
}

.file-icon {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  padding: 16rpx;
  box-shadow:
    4rpx 4rpx 8rpx rgba(210, 71, 38, 0.1),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
}

.file-details {
  flex: 1;
}

.file-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.file-size-info {
  font-size: 24rpx;
  color: #5a6c7d;
}

.size-label {
  color: #8a9aa9;
}

.size-value {
  color: #2c3e50;
  font-weight: 500;
}

.size-value.converted {
  color: #D24726;
  font-weight: 600;
}

.size-value.placeholder {
  color: #bbb;
  font-style: italic;
}

.size-separator {
  margin: 0 16rpx;
}

.remove-btn {
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #666;
  box-shadow:
    4rpx 4rpx 8rpx rgba(210, 71, 38, 0.1),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.remove-btn:active {
  transform: scale(0.95);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(210, 71, 38, 0.1),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}





/* 页面范围选项 */
.range-options {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.range-item {
  flex: 1;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow:
    4rpx 4rpx 8rpx rgba(210, 71, 38, 0.1),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
}

.range-item.active {
  background: linear-gradient(135deg, #D24726 0%, #FF7F50 100%);
  color: white;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 0.2),
    inset -2rpx -2rpx 4rpx rgba(0, 0, 0, 0.2),
    0 0 16rpx rgba(210, 71, 38, 0.3);
}

.range-item:active {
  transform: scale(0.98);
}

.range-text {
  font-size: 26rpx;
  font-weight: 500;
}

/* 自定义范围输入 */
.custom-range-input {
  margin-top: 16rpx;
}

.range-input {
  width: 100%;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  font-size: 26rpx;
  color: #2c3e50;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(210, 71, 38, 0.1),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.input-tip {
  display: block;
  font-size: 22rpx;
  color: #8a9aa9;
  margin-top: 12rpx;
  padding-left: 8rpx;
}



/* 转换进行中 */
.converting-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
}

.progress-card {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
  box-shadow:
    12rpx 12rpx 24rpx rgba(210, 71, 38, 0.15),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.progress-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  box-shadow:
    8rpx 8rpx 16rpx rgba(210, 71, 38, 0.15),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8),
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 0.8),
    inset -2rpx -2rpx 4rpx rgba(210, 71, 38, 0.1);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.progress-svg {
  width: 64rpx;
  height: 64rpx;
}

.progress-content {
  width: 100%;
}

.progress-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.progress-subtitle {
  font-size: 28rpx;
  color: #5a6c7d;
  margin-bottom: 40rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: rgba(210, 71, 38, 0.1);
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(210, 71, 38, 0.2),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #D24726 0%, #FF7F50 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
  box-shadow: 0 0 8rpx rgba(210, 71, 38, 0.3);
}

.progress-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #D24726;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 转换完成 */
.result-section {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
}

.result-card {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
  box-shadow:
    12rpx 12rpx 24rpx rgba(210, 71, 38, 0.15),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.result-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  box-shadow:
    8rpx 8rpx 16rpx rgba(210, 71, 38, 0.15),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8),
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 0.8),
    inset -2rpx -2rpx 4rpx rgba(210, 71, 38, 0.1);
}

.result-svg {
  width: 64rpx;
  height: 64rpx;
}

.result-content {
  width: 100%;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.result-subtitle {
  font-size: 28rpx;
  color: #5a6c7d;
  margin-bottom: 24rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.result-info {
  font-size: 24rpx;
  color: #5a6c7d;
  margin-bottom: 40rpx;
}

.info-label {
  color: #8a9aa9;
}

.info-value {
  color: #2c3e50;
  font-weight: 500;
}

.info-separator {
  margin: 0 16rpx;
}

.result-actions {
  display: flex;
  gap: 24rpx;
  width: 100%;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.98);
}

.action-btn.primary {
  background: linear-gradient(135deg, #D24726 0%, #FF7F50 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(210, 71, 38, 0.3),
    0 2rpx 6rpx rgba(210, 71, 38, 0.2);
}

.action-btn.secondary {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #666666;
  box-shadow:
    0 6rpx 24rpx rgba(0, 0, 0, 0.1),
    0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

/* 页面范围设置面板 */
.page-range-panel {
  position: fixed;
  bottom: 170rpx; /* 在底部按钮上方，增加间距 */
  left: 32rpx;
  right: 32rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow:
    0 8rpx 32rpx rgba(210, 71, 38, 0.15),
    0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  z-index: 999;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.panel-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

.panel-value {
  font-size: 28rpx;
  color: #D24726;
  font-weight: 500;
}

/* 底部胶囊按钮 */
.bottom-capsule {
  position: fixed;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 36rpx;
  z-index: 1000;
}

.capsule-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 17rpx 48rpx;
  border-radius: 42rpx;
  font-size: 28rpx;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  min-width: 240rpx;
  height: 55rpx;
}

.capsule-btn:active {
  transform: scale(0.96);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.16),
    0 1rpx 2rpx rgba(0, 0, 0, 0.12);
}

.capsule-btn.left {
  background: linear-gradient(135deg, #D24726 0%, #FF7F50 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(210, 71, 38, 0.3),
    0 2rpx 6rpx rgba(210, 71, 38, 0.2);
}

.capsule-btn.right {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(39, 174, 96, 0.3),
    0 2rpx 6rpx rgba(39, 174, 96, 0.2);
}

.capsule-btn.disabled {
  background: linear-gradient(135deg, #CCCCCC 0%, #999999 100%);
  color: rgba(255, 255, 255, 0.7);
  pointer-events: none;
  box-shadow:
    0 6rpx 24rpx rgba(0, 0, 0, 0.12),
    0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.capsule-text {
  font-size: 28rpx;
  font-weight: 600;
  color: inherit;
}
