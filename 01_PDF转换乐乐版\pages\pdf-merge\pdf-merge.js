// pages/pdf-merge/pdf-merge.js
Page({
  data: {
    statusBarHeight: 0,
    navbarHeight: 0,
    menuButtonTopRpx: 0,
    menuButtonHeightRpx: 0,

    // 文件状态
    selectedFiles: [],
    merging: false,
    merged: false,

    // 合并设置
    mergedFileName: '合并文件',

    // 合并进度
    progressPercent: 0,

    // 合并结果
    mergedFileSize: '',
    mergedFilePath: ''
  },

  onLoad(options) {
    this.initNavbar()
    // 页面加载后直接选择PDF文件
    setTimeout(() => {
      this.onSelectFiles()
    }, 500) // 延迟500ms确保页面完全加载
  },

  // 初始化导航栏
  initNavbar() {
    const systemInfo = wx.getSystemInfoSync()
    const statusBarHeight = systemInfo.statusBarHeight
    const navbarHeight = statusBarHeight + 44

    const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
    const screenWidth = systemInfo.screenWidth
    const rpxRatio = 750 / screenWidth
    const menuButtonTopRpx = (menuButtonInfo.top - statusBarHeight + 1) * rpxRatio
    const menuButtonHeightRpx = menuButtonInfo.height * rpxRatio

    this.setData({
      statusBarHeight,
      navbarHeight,
      menuButtonTopRpx,
      menuButtonHeightRpx
    })
  },

  // 返回按钮
  onBackClick() {
    wx.navigateBack()
  },

  // 选择文件
  onSelectFiles() {
    wx.showActionSheet({
      itemList: ['从文件选择', '从聊天记录选择'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 从文件选择
          this.selectFromLocal()
        } else if (res.tapIndex === 1) {
          // 从聊天记录选择
          this.selectFromChat()
        }
      }
    })
  },

  // 从本地文件选择
  selectFromLocal() {
    wx.chooseMessageFile({
      count: 9,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        this.handleSelectedFiles(res.tempFiles.map(file => ({
          name: file.name,
          size: file.size,
          path: file.path
        })))
      },
      fail: (err) => {
        console.error('选择文件失败:', err)
        wx.showToast({
          title: '选择文件失败',
          icon: 'none'
        })
      }
    })
  },

  // 从聊天记录选择
  selectFromChat() {
    wx.chooseMessageFile({
      count: 9,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        this.handleSelectedFiles(res.tempFiles.map(file => ({
          name: file.name,
          size: file.size,
          path: file.path
        })))
      },
      fail: (err) => {
        console.error('选择文件失败:', err)
      }
    })
  },

  // 处理选中的文件
  handleSelectedFiles(files) {
    const newFiles = files.map((file, index) => ({
      id: Date.now() + index,
      name: file.name,
      size: this.formatFileSize(file.size),
      path: file.path,
      originalSize: file.size
    }))

    // 合并新选择的文件到现有列表
    const allFiles = [...this.data.selectedFiles, ...newFiles]

    this.setData({
      selectedFiles: allFiles
    })

    // 如果是第一次选择文件，生成默认文件名
    if (this.data.selectedFiles.length === 0) {
      this.setData({
        mergedFileName: this.generateDefaultFileName(allFiles)
      })
    }
  },

  // 生成默认文件名
  generateDefaultFileName(files) {
    if (files.length === 0) return '合并文件'
    if (files.length === 1) return files[0].name.replace('.pdf', '') + '_合并'
    return `${files.length}个文件合并`
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 文件名输入
  onFileNameInput(e) {
    this.setData({
      mergedFileName: e.detail.value
    })
  },

  // 向上移动文件
  onMoveUp(e) {
    const index = e.currentTarget.dataset.index
    if (index <= 0) return

    const files = [...this.data.selectedFiles]
    const temp = files[index]
    files[index] = files[index - 1]
    files[index - 1] = temp

    this.setData({
      selectedFiles: files
    })
  },

  // 向下移动文件
  onMoveDown(e) {
    const index = e.currentTarget.dataset.index
    const files = [...this.data.selectedFiles]
    if (index >= files.length - 1) return

    const temp = files[index]
    files[index] = files[index + 1]
    files[index + 1] = temp

    this.setData({
      selectedFiles: files
    })
  },

  // 移除文件
  onRemoveFile(e) {
    const index = e.currentTarget.dataset.index
    const files = [...this.data.selectedFiles]
    files.splice(index, 1)

    this.setData({
      selectedFiles: files
    })

    // 如果没有文件了，重置状态
    if (files.length === 0) {
      this.setData({
        mergedFileName: '合并文件',
        merged: false,
        merging: false
      })
    }
  },

  // 开始合并
  onStartMerge() {
    if (this.data.selectedFiles.length < 2) {
      wx.showToast({
        title: '至少需要2个文件才能合并',
        icon: 'none'
      })
      return
    }

    if (!this.data.mergedFileName.trim()) {
      wx.showToast({
        title: '请输入合并后的文件名',
        icon: 'none'
      })
      return
    }

    this.setData({
      merging: true,
      progressPercent: 0
    })

    this.simulateMergeProgress()
  },

  // 模拟合并进度
  simulateMergeProgress() {
    let progress = 0
    const interval = setInterval(() => {
      progress += Math.random() * 15 + 5
      if (progress >= 100) {
        progress = 100
        clearInterval(interval)
        this.completeMerge()
      }
      this.setData({
        progressPercent: Math.floor(progress)
      })
    }, 200)
  },

  // 完成合并
  completeMerge() {
    // 计算合并后的文件大小（模拟）
    const totalSize = this.data.selectedFiles.reduce((sum, file) => sum + file.originalSize, 0)
    const mergedSize = totalSize * 0.95 // 假设合并后稍微小一点

    setTimeout(() => {
      this.setData({
        merging: false,
        merged: true,
        mergedFileSize: this.formatFileSize(mergedSize),
        mergedFilePath: '/temp/merged.pdf' // 模拟路径
      })
    }, 500)
  },

  // 合并其他文件
  onMergeAnother() {
    this.setData({
      selectedFiles: [],
      merging: false,
      merged: false,
      mergedFileName: '合并文件',
      progressPercent: 0,
      mergedFileSize: '',
      mergedFilePath: ''
    })
  },

  // 下载合并文件
  onDownloadPDF() {
    if (!this.data.merged) {
      wx.showToast({
        title: '请先完成合并',
        icon: 'none'
      })
      return
    }

    // 这里应该实现实际的下载逻辑
    wx.showToast({
      title: '下载功能开发中',
      icon: 'none'
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})