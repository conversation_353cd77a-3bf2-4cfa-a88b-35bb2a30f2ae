# PDF转PPT功能文档

## 📋 项目概述

本文档记录PDF转PPT功能的后端API参数、微信小程序配置和相关技术规范。

## 🔧 后端API配置 - Stirling-PDF

### 基本信息
- **项目地址**: https://github.com/Stirling-Tools/Stirling-PDF
- **API端点**: `/pdf-to-ppt` 或 `/convert/pdf/ppt`
- **请求方法**: POST
- **内容类型**: multipart/form-data

### 输入参数

#### 必需参数
```javascript
{
  fileInput: File,           // PDF文件（必需）
}
```

#### 可选参数
```javascript
{
  pageRange: String,         // 页面范围：'all' | 'custom'
  customPageRange: String,   // 自定义页面范围，如 "1-5,8,10-12"
  preserveImages: Boolean,   // 是否保留图片质量
  extractText: Boolean,      // 是否提取文本内容
  slideLayout: String        // 幻灯片布局：'auto' | 'single' | 'multiple'
}
```

### 默认配置说明

为了简化用户操作，以下参数使用最佳默认值：
- **输出格式**: 固定为 PPTX (PowerPoint 2007+)
- **转换质量**: 固定为高质量
- **图片分辨率**: 固定为高分辨率 (300 DPI)

#### pageRange (页面范围)
- **all**: 转换所有页面（默认）
- **custom**: 自定义页面范围

#### customPageRange (自定义页面范围)
支持格式：
- 单页：`1`, `5`, `10`
- 范围：`1-5`, `10-15`
- 混合：`1-5,8,10-12,20`

### API调用示例

#### cURL示例
```bash
curl -X POST "http://your-server:8080/pdf-to-ppt" \
  -H "Content-Type: multipart/form-data" \
  -H "X-API-KEY: your-api-key" \
  -F "fileInput=@/path/to/file.pdf" \
  -F "pageRange=all" \
  -F "preserveImages=true" \
  -F "extractText=true" \
  --output converted_presentation.pptx
```

#### JavaScript示例
```javascript
const formData = new FormData();
formData.append('fileInput', pdfFile);
formData.append('pageRange', 'all');
formData.append('preserveImages', 'true');
formData.append('extractText', 'true');

fetch('http://your-server:8080/pdf-to-ppt', {
  method: 'POST',
  headers: {
    'X-API-KEY': 'your-api-key'
  },
  body: formData
})
.then(response => response.blob())
.then(blob => {
  // 处理转换后的PPTX文件
});
```

## 📱 微信小程序配置

### 页面配置
```json
{
  "usingComponents": {},
  "navigationStyle": "custom",
  "backgroundColor": "#f5f7fa"
}
```

### 转换选项映射
```javascript
// 小程序选项到API参数的映射
const convertOptions = {
  outputFormat: this.data.outputFormat,     // 'pptx' | 'ppt'
  quality: this.data.quality,               // 'fast' | 'medium' | 'high'
  imageResolution: this.data.imageResolution, // 'low' | 'medium' | 'high'
  pageRange: this.data.pageRange,           // 'all' | 'custom'
  customPageRange: this.data.customPageRange, // "1-5,8,10-12"
  preserveImages: true,
  extractText: true,
  slideLayout: 'auto'
};
```

### 文件上传实现
```javascript
const uploadPDFForConversion = (filePath, options = {}) => {
  const {
    outputFormat = 'pptx',
    quality = 'medium',
    imageResolution = 'medium',
    pageRange = 'all',
    customPageRange = '',
    preserveImages = true,
    extractText = true
  } = options;

  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: 'https://your-server.com/pdf-to-ppt',
      filePath: filePath,
      name: 'fileInput',
      formData: {
        outputFormat,
        quality,
        imageResolution,
        pageRange,
        customPageRange,
        preserveImages: preserveImages.toString(),
        extractText: extractText.toString()
      },
      header: {
        'X-API-KEY': 'your-api-key'
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res);
        } else {
          reject(new Error(`转换失败: ${res.statusCode}`));
        }
      },
      fail: reject
    });
  });
};
```

## 🎨 UI设计规范

### 主题色彩
- **主色调**: #D24726 (橙红色)
- **辅助色**: #FF7F50 (珊瑚色)
- **背景色**: #f8f9fa (浅灰色)
- **文字色**: #2c3e50 (深蓝灰)

### 组件样式
- **圆角**: 16rpx - 32rpx
- **阴影**: 新拟物风格
- **动画**: 0.3s ease 过渡
- **按钮**: 渐变背景 + 阴影

### 响应式设计
- 支持不同屏幕尺寸
- 自适应布局
- 触摸友好的交互

## 🔒 安全配置

### API认证
```javascript
headers: {
  'X-API-KEY': 'your-api-key'
}
```

### 文件大小限制
- 单文件最大: 50MB
- 支持的格式: PDF
- 输出格式: PPT, PPTX

## 📊 性能优化

### 转换时间估算
- 快速模式: ~30秒/10页
- 标准模式: ~60秒/10页  
- 高质量模式: ~120秒/10页

### 文件大小估算
- 输出文件通常比原PDF大1.5-2倍
- 高分辨率图片会显著增加文件大小
- PPTX格式比PPT格式文件更小

## 🐛 错误处理

### 常见错误码
- 400: 参数错误
- 413: 文件过大
- 415: 不支持的文件格式
- 500: 服务器内部错误

### 错误处理示例
```javascript
.catch(error => {
  console.error('转换失败:', error);
  wx.showToast({
    title: '转换失败，请重试',
    icon: 'none'
  });
});
```
