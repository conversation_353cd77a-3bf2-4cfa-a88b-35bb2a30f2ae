/* pages/pdf-split/pdf-split.wxss */

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  position: relative;
}

.navbar-back {
  position: absolute;
  left: 32rpx;
  width: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.navbar-back:active {
  opacity: 0.6;
  transform: scale(0.9);
}

.navbar-back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  position: absolute;
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 32rpx;
  margin-top: 30rpx;
}

/* 文件上传区域 */
.upload-section {
  margin-bottom: 48rpx;
}

.upload-area {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 80rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 4rpx dashed rgba(163, 177, 198, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 新拟物设计阴影 */
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.upload-area:active {
  transform: scale(0.98);
  box-shadow:
    inset 6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    inset -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.upload-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(220, 20, 60, 0.3),
    0 0 8rpx rgba(220, 20, 60, 0.2);
}

.upload-svg {
  width: 80rpx;
  height: 80rpx;
  opacity: 0.8;
}

.upload-content {
  flex: 1;
}

.upload-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.upload-subtitle {
  font-size: 28rpx;
  color: #5a6c7d;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.upload-tips {
  font-size: 24rpx;
  color: #8a9aa9;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

/* 文件信息区域 */
.file-info-card {
  background: #f0f2f5;
  border-radius: 24rpx;
  padding: 30rpx 52rpx;
  margin: 0 -32rpx 32rpx -32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow:
    8rpx 8rpx 16rpx rgba(163, 177, 198, 0.4),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
}

.file-icon {
  width: 100rpx;
  height: 100rpx;
  flex-shrink: 0;
  padding: 12rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(163, 177, 198, 0.3);
}

.file-details {
  flex: 1;
}

.file-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.file-info {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  flex-wrap: wrap;
}

.info-label {
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.info-separator {
  margin: 0 16rpx;
}

.info-value {
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.remove-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  text-align: center;
  background: rgba(220, 20, 60, 0.1);
  color: #DC143C;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.3),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.remove-btn:active {
  transform: scale(0.95);
  box-shadow:
    inset 4rpx 4rpx 8rpx rgba(163, 177, 198, 0.4),
    inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.9);
}

/* 主要内容区域 */
.main-content {
  padding: 30rpx;
  padding-bottom: 260rpx; /* 为底部固定区域预留空间 */
}

/* 底部固定区域 */
.footer-area {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 80rpx;
  padding: 20rpx 30rpx calc(20rpx + env(safe-area-inset-bottom));
  z-index: 100;
}

/* 拆分设置区域 */
.split-settings {
  background: rgba(240, 242, 245, 0.98);
  backdrop-filter: blur(10px);
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.setting-item {
  margin-bottom: 15rpx;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.setting-label {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.setting-value {
  min-width: 60rpx;
  text-align: center;
  font-size: 28rpx;
  color: #DC143C;
  font-weight: 500;
  padding: 4rpx 16rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.3);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.3),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.setting-value-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.3);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.3),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.setting-value-btn:active {
  transform: scale(0.95);
  box-shadow:
    inset 4rpx 4rpx 8rpx rgba(163, 177, 198, 0.4),
    inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.9);
}

.setting-arrow {
  font-size: 24rpx;
  color: #5a6c7d;
  font-weight: bold;
}

.slider-container {
  padding: 30rpx 20rpx;
  border-radius: 20rpx;
  background: #f0f2f5;
  box-shadow:
    inset 6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    inset -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.slider-tips {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
  font-size: 24rpx;
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.tip-left, .tip-right {
  font-size: 24rpx;
  color: #5a6c7d;
}

/* 页码范围输入 */
.range-inputs {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.range-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.range-label {
  font-size: 24rpx;
  color: #5a6c7d;
  text-align: center;
}

.range-input {
  padding: 16rpx;
  border-radius: 12rpx;
  border: 2rpx solid rgba(163, 177, 198, 0.3);
  background: rgba(255, 255, 255, 0.8);
  text-align: center;
  font-size: 28rpx;
  color: #2c3e50;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.2),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.9);
}

.range-input:focus {
  border-color: rgba(220, 20, 60, 0.5);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.1),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 1),
    0 0 0 4rpx rgba(220, 20, 60, 0.1);
}

.range-separator {
  font-size: 32rpx;
  color: #5a6c7d;
  font-weight: bold;
  margin-top: 24rpx;
}

.range-tip {
  font-size: 24rpx;
  color: #8a9aa9;
  text-align: center;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

/* 预估结果 */
.estimate-section {
  margin-top: 32rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16rpx;
  border: 1rpx solid rgba(163, 177, 198, 0.3);
}

.estimate-header {
  margin-bottom: 20rpx;
}

.estimate-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #2c3e50;
}

.estimate-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.estimate-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.estimate-label {
  font-size: 24rpx;
  color: #8a9aa9;
}

.estimate-value {
  font-size: 24rpx;
  color: #2c3e50;
  font-weight: 500;
}

/* 拆分方式选择弹窗 */
.split-mode-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(163, 177, 198, 0.3);
  backdrop-filter: blur(20rpx);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(20rpx);
  }
}

.modal-content {
  background: rgba(240, 242, 245, 0.95);
  backdrop-filter: blur(30rpx);
  border-radius: 32rpx;
  width: 100%;
  max-width: 600rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.4),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
  animation: slideUp 0.4s ease-out;
  overflow: hidden;
}

@keyframes slideUp {
  from {
    transform: translateY(80rpx) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.modal-header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  padding: 40rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  border-bottom: 1rpx solid rgba(163, 177, 198, 0.2);
  box-shadow:
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8),
    0 1rpx 3rpx rgba(163, 177, 198, 0.1);
}

.modal-title {
  color: #2c3e50;
  font-size: 32rpx;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow:
    4rpx 4rpx 8rpx rgba(163, 177, 198, 0.3),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.modal-close:active {
  transform: scale(0.95);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.3),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.close-icon {
  color: #5a6c7d;
  font-size: 28rpx;
  font-weight: bold;
}

.modal-body {
  padding: 48rpx 32rpx;
  background: rgba(255, 255, 255, 0.4);
}

.split-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.split-option {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
  box-shadow:
    4rpx 4rpx 8rpx rgba(163, 177, 198, 0.4),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
}

.split-option.active {
  background: linear-gradient(135deg, #DC143C 0%, #FF6B6B 100%);
  color: white;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 0.2),
    inset -2rpx -2rpx 4rpx rgba(0, 0, 0, 0.2),
    0 0 16rpx rgba(220, 20, 60, 0.3);
}

.split-option.active .option-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.1),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.3);
}

.split-option.active .icon-pages::after,
.split-option.active .icon-range::before,
.split-option.active .icon-range::after,
.split-option.active .icon-single::before,
.split-option.active .icon-single::after {
  border-color: rgba(255, 255, 255, 0.8);
}

.split-option:active {
  transform: scale(0.98);
}

.option-icon {
  margin-right: 24rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 0.8),
    inset -2rpx -2rpx 4rpx rgba(163, 177, 198, 0.3);
}

/* 按页数拆分图标 - 多个文档堆叠 */
.icon-pages {
  position: relative;
  width: 48rpx;
  height: 48rpx;
}

.icon-pages::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 32rpx;
  height: 40rpx;
  background: currentColor;
  border-radius: 4rpx;
  opacity: 0.3;
}

.icon-pages::after {
  content: '';
  position: absolute;
  top: 8rpx;
  left: 16rpx;
  width: 32rpx;
  height: 40rpx;
  background: currentColor;
  border-radius: 4rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
}

/* 按页码范围拆分图标 - 选择框 */
.icon-range {
  position: relative;
  width: 48rpx;
  height: 48rpx;
}

.icon-range::before {
  content: '';
  position: absolute;
  top: 4rpx;
  left: 8rpx;
  width: 32rpx;
  height: 40rpx;
  background: transparent;
  border: 2rpx solid currentColor;
  border-radius: 4rpx;
}

.icon-range::after {
  content: '';
  position: absolute;
  top: 18rpx;
  left: 12rpx;
  width: 24rpx;
  height: 12rpx;
  background: currentColor;
  border-radius: 2rpx;
  opacity: 0.6;
}

/* 拆分为单页图标 - 分离的页面 */
.icon-single {
  position: relative;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.icon-single::before {
  content: '';
  width: 18rpx;
  height: 32rpx;
  background: currentColor;
  border-radius: 3rpx;
  opacity: 0.8;
}

.icon-single::after {
  content: '';
  width: 18rpx;
  height: 32rpx;
  background: currentColor;
  border-radius: 3rpx;
  opacity: 0.8;
}

.option-info {
  flex: 1;
}

.option-name {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.option-desc {
  font-size: 24rpx;
  opacity: 0.8;
}

.modal-footer {
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid rgba(163, 177, 198, 0.2);
  display: flex;
  gap: 24rpx;
  box-shadow:
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.modal-btn:active {
  transform: scale(0.96);
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.8);
  color: #5a6c7d;
  box-shadow:
    6rpx 6rpx 12rpx rgba(163, 177, 198, 0.3),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.cancel-btn:active {
  box-shadow:
    inset 3rpx 3rpx 6rpx rgba(163, 177, 198, 0.3),
    inset -3rpx -3rpx 6rpx rgba(255, 255, 255, 0.8);
}

.confirm-btn {
  background: rgba(220, 20, 60, 0.9);
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  box-shadow:
    6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2),
    0 0 20rpx rgba(220, 20, 60, 0.3);
}

.confirm-btn:active {
  box-shadow:
    inset 3rpx 3rpx 6rpx rgba(163, 177, 198, 0.4),
    inset -3rpx -3rpx 6rpx rgba(255, 255, 255, 0.8),
    0 0 15rpx rgba(220, 20, 60, 0.4);
}

/* 拆分进行中 */
.splitting-section {
  margin-bottom: 48rpx;
}

.progress-card {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.progress-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(220, 20, 60, 0.3),
    0 0 8rpx rgba(220, 20, 60, 0.2);
}

.progress-svg {
  width: 80rpx;
  height: 80rpx;
  opacity: 0.8;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.progress-content {
  width: 100%;
}

.progress-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.progress-subtitle {
  font-size: 26rpx;
  color: #5a6c7d;
  margin-bottom: 40rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: rgba(163, 177, 198, 0.3);
  border-radius: 6rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.4),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #DC143C 0%, #FF6B6B 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
  box-shadow: 0 0 8rpx rgba(220, 20, 60, 0.3);
}

.progress-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #DC143C;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 拆分结果 */
.result-section {
  margin-bottom: 48rpx;
}

.result-card {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.result-icon {
  width: 80rpx;
  height: 80rpx;
  margin: 0 auto 16rpx;
  padding: 16rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(50, 205, 50, 0.3),
    0 0 8rpx rgba(50, 205, 50, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.result-svg {
  width: 48rpx;
  height: 48rpx;
}

.result-content {
  text-align: center;
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #27ae60;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.result-subtitle {
  font-size: 26rpx;
  color: #5a6c7d;
  margin-bottom: 32rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

/* 结果文件列表 */
.result-files {
  margin-bottom: 32rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.result-file {
  display: flex;
  align-items: center;
  padding: 16rpx;
  margin-bottom: 12rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16rpx;
  box-shadow:
    4rpx 4rpx 8rpx rgba(163, 177, 198, 0.3),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8);
}

.result-file:last-child {
  margin-bottom: 0;
}

.file-icon-small {
  width: 60rpx;
  height: 60rpx;
  margin-right: 16rpx;
  padding: 8rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.8);
  box-shadow:
    inset 1rpx 1rpx 2rpx rgba(255, 255, 255, 1),
    inset -1rpx -1rpx 2rpx rgba(163, 177, 198, 0.3);
}

.file-info-small {
  flex: 1;
}

.file-name-small {
  font-size: 26rpx;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4rpx;
}

.file-pages-small {
  font-size: 22rpx;
  color: #8a9aa9;
}

.download-btn-small {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
  box-shadow:
    inset 1rpx 1rpx 2rpx rgba(163, 177, 198, 0.3),
    inset -1rpx -1rpx 2rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.download-btn-small:active {
  transform: scale(0.95);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.4),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.9);
}

/* 结果操作按钮 */
.result-actions {
  display: flex;
  gap: 24rpx;
  justify-content: center;
}

.action-btn.secondary {
  background: #f0f2f5;
  color: #5a6c7d;
  padding: 20rpx 40rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow:
    6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.action-btn.primary {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow:
    6rpx 6rpx 12rpx rgba(39, 174, 96, 0.3),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
}

.action-btn:active {
  transform: scale(0.95);
}

/* 底部胶囊按钮 */
.bottom-capsule {
  position: fixed;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 24rpx;
  z-index: 1000;
}

.capsule-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 17rpx 48rpx;
  border-radius: 42rpx;
  box-shadow:
    0 6rpx 24rpx rgba(0, 0, 0, 0.12),
    0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  min-width: 240rpx;
  height: 55rpx;
}

.capsule-btn:active {
  transform: scale(0.96);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.16),
    0 1rpx 2rpx rgba(0, 0, 0, 0.12);
}

.capsule-btn.left {
  background: linear-gradient(135deg, #DC143C 0%, #FF6B6B 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(220, 20, 60, 0.3),
    0 2rpx 6rpx rgba(220, 20, 60, 0.2);
}

.capsule-btn.right {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(39, 174, 96, 0.3),
    0 2rpx 6rpx rgba(39, 174, 96, 0.2);
}

.capsule-btn.disabled {
  background: linear-gradient(135deg, #CCCCCC 0%, #999999 100%);
  color: rgba(255, 255, 255, 0.7);
  pointer-events: none;
  box-shadow:
    0 6rpx 24rpx rgba(0, 0, 0, 0.12),
    0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.capsule-text {
  font-size: 39rpx;
  font-weight: 500;
  line-height: 1;
}