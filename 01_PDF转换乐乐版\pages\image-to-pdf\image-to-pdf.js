// pages/image-to-pdf/image-to-pdf.js
Page({
  data: {
    // 导航栏相关
    statusBarHeight: 0,
    navbarHeight: 0,
    menuButtonTopRpx: 0,
    menuButtonHeightRpx: 0,
    
    // 图片相关
    selectedImages: [],
    hasImages: false,
    
    // 设置选项
    fitOption: 'fitDocumentToImage', // fitDocumentToImage, fitImageToDocument, maintainAspectRatio
    colorType: 'color', // color, greyscale, blackwhite
    autoRotate: false,
    
    // 转换状态
    converting: false,
    converted: false,
    progressPercent: 0,
    
    // 结果相关
    pdfPreviewUrl: '',
    pdfFileName: '',
    pdfFileSize: '',
    
    // 文本映射
    fitOptionText: '文档适配图片',
    colorTypeText: '彩色'
  },

  onLoad() {
    this.initNavbar();
    // 页面加载后直接选择图片
    setTimeout(() => {
      this.onSelectImages();
    }, 500); // 延迟500ms确保页面完全加载
  },

  // 初始化导航栏
  initNavbar() {
    const systemInfo = wx.getSystemInfoSync();
    const menuButton = wx.getMenuButtonBoundingClientRect();
    
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navbarHeight: systemInfo.statusBarHeight + 44,
      menuButtonTopRpx: (menuButton.top - systemInfo.statusBarHeight) * 2,
      menuButtonHeightRpx: menuButton.height * 2
    });
  },

  // 返回按钮
  onBackClick() {
    wx.navigateBack();
  },

  // 选择图片
  onSelectImages() {
    wx.showActionSheet({
      itemList: ['从相册选择', '拍照', '从聊天记录选择'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 从相册选择
          this.selectFromAlbum();
        } else if (res.tapIndex === 1) {
          // 拍照
          this.selectFromCamera();
        } else if (res.tapIndex === 2) {
          // 从聊天记录选择
          this.selectFromChat();
        }
      }
    });
  },

  // 从相册选择图片
  selectFromAlbum() {
    wx.chooseMedia({
      count: 9,
      mediaType: ['image'],
      sourceType: ['album'],
      success: (res) => {
        this.processSelectedImages(res.tempFiles);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 拍照
  selectFromCamera() {
    wx.chooseMedia({
      count: 1, // 拍照一次只能拍一张
      mediaType: ['image'],
      sourceType: ['camera'],
      success: (res) => {
        // 如果已有图片，则添加到现有列表
        if (this.data.hasImages) {
          const newImages = this.processImageFiles(res.tempFiles);
          const allImages = [...this.data.selectedImages, ...newImages];
          if (allImages.length > 9) {
            wx.showToast({
              title: '最多只能选择9张图片',
              icon: 'none'
            });
            return;
          }
          this.setData({
            selectedImages: allImages,
            hasImages: allImages.length > 0
          });
        } else {
          this.processSelectedImages(res.tempFiles);
        }
      },
      fail: (err) => {
        console.error('拍照失败:', err);
        wx.showToast({
          title: '拍照失败',
          icon: 'none'
        });
      }
    });
  },

  // 从聊天记录选择图片
  selectFromChat() {
    wx.chooseMessageFile({
      count: 9,
      type: 'image',
      success: (res) => {
        // 转换聊天记录文件格式
        const tempFiles = res.tempFiles.map(file => ({
          tempFilePath: file.path,
          size: file.size
        }));
        this.processSelectedImages(tempFiles);
      },
      fail: (err) => {
        console.error('从聊天记录选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 添加更多图片
  onAddMoreImages() {
    const remainingCount = 9 - this.data.selectedImages.length;
    if (remainingCount <= 0) {
      wx.showToast({
        title: '最多只能选择9张图片',
        icon: 'none'
      });
      return;
    }

    wx.showActionSheet({
      itemList: ['从相册选择', '拍照', '从聊天记录选择'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 从相册选择
          this.addFromAlbum(remainingCount);
        } else if (res.tapIndex === 1) {
          // 拍照
          this.selectFromCamera(); // 拍照逻辑已经处理了添加到现有列表
        } else if (res.tapIndex === 2) {
          // 从聊天记录选择
          this.addFromChat(remainingCount);
        }
      }
    });
  },

  // 从相册添加更多图片
  addFromAlbum(remainingCount) {
    wx.chooseMedia({
      count: remainingCount,
      mediaType: ['image'],
      sourceType: ['album'],
      success: (res) => {
        const newImages = this.processImageFiles(res.tempFiles);
        const allImages = [...this.data.selectedImages, ...newImages];
        this.setData({
          selectedImages: allImages,
          hasImages: allImages.length > 0
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 从聊天记录添加更多图片
  addFromChat(remainingCount) {
    wx.chooseMessageFile({
      count: remainingCount,
      type: 'image',
      success: (res) => {
        // 转换聊天记录文件格式
        const tempFiles = res.tempFiles.map(file => ({
          tempFilePath: file.path,
          size: file.size
        }));
        const newImages = this.processImageFiles(tempFiles);
        const allImages = [...this.data.selectedImages, ...newImages];
        this.setData({
          selectedImages: allImages,
          hasImages: allImages.length > 0
        });
      },
      fail: (err) => {
        console.error('从聊天记录选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 处理选择的图片
  processSelectedImages(files) {
    const images = this.processImageFiles(files);
    this.setData({
      selectedImages: images,
      hasImages: images.length > 0
    });
  },

  // 处理图片文件信息
  processImageFiles(files) {
    return files.map((file, index) => ({
      id: Date.now() + index,
      path: file.tempFilePath,
      name: `图片${index + 1}.${file.tempFilePath.split('.').pop()}`,
      size: this.formatFileSize(file.size)
    }));
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // 移除图片
  onRemoveImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.selectedImages;
    images.splice(index, 1);
    this.setData({
      selectedImages: images,
      hasImages: images.length > 0
    });
  },

  // 上移图片
  onMoveUp(e) {
    const index = e.currentTarget.dataset.index;
    if (index <= 0) return;
    
    const images = [...this.data.selectedImages];
    [images[index], images[index - 1]] = [images[index - 1], images[index]];
    this.setData({ selectedImages: images });
  },

  // 下移图片
  onMoveDown(e) {
    const index = e.currentTarget.dataset.index;
    if (index >= this.data.selectedImages.length - 1) return;
    
    const images = [...this.data.selectedImages];
    [images[index], images[index + 1]] = [images[index + 1], images[index]];
    this.setData({ selectedImages: images });
  },

  // 页面适配选项改变
  onFitOptionChange(e) {
    const option = e.currentTarget.dataset.option;
    const textMap = {
      'fitDocumentToImage': '文档适配图片',
      'fitImageToDocument': '图片适配文档',
      'maintainAspectRatio': '保持宽高比'
    };
    
    this.setData({
      fitOption: option,
      fitOptionText: textMap[option]
    });
  },

  // 颜色类型改变
  onColorTypeChange(e) {
    const type = e.currentTarget.dataset.type;
    const textMap = {
      'color': '彩色',
      'greyscale': '灰度',
      'blackwhite': '黑白'
    };
    
    this.setData({
      colorType: type,
      colorTypeText: textMap[type]
    });
  },

  // 自动旋转开关
  onAutoRotateChange(e) {
    this.setData({
      autoRotate: e.detail.value
    });
  },

  // 开始转换
  onStartConversion() {
    if (!this.data.hasImages) {
      wx.showToast({
        title: '请先选择图片',
        icon: 'none'
      });
      return;
    }

    this.setData({
      converting: true,
      progressPercent: 0
    });

    // 模拟转换进度
    this.simulateProgress();
    
    // 调用转换API
    this.convertImagesToPDF();
  },

  // 模拟进度条
  simulateProgress() {
    let progress = 0;
    const timer = setInterval(() => {
      progress += Math.random() * 15;
      if (progress >= 95) {
        progress = 95;
        clearInterval(timer);
      }
      this.setData({ progressPercent: Math.floor(progress) });
    }, 200);
  },

  // 转换图片为PDF
  async convertImagesToPDF() {
    try {
      // 准备FormData
      const formData = new FormData();

      // 添加图片文件
      for (let i = 0; i < this.data.selectedImages.length; i++) {
        const image = this.data.selectedImages[i];
        // 在实际实现中，需要将微信临时文件路径转换为File对象
        // 这里需要通过wx.uploadFile或其他方式处理
        formData.append('fileInput', image.file);
      }

      // 添加转换参数
      formData.append('fitOption', this.data.fitOption);
      formData.append('colorType', this.data.colorType);
      formData.append('autoRotate', this.data.autoRotate.toString());

      // 调用Stirling-PDF API
      const response = await this.callStirlingPDFAPI(formData);

      if (response.success) {
        this.setData({
          converting: false,
          converted: true,
          progressPercent: 100,
          pdfFileName: response.fileName || 'converted_images.pdf',
          pdfFileSize: response.fileSize || '未知大小',
          pdfPreviewUrl: response.previewUrl || '/images/pdf-icon.svg'
        });
      } else {
        throw new Error(response.message || '转换失败');
      }

    } catch (error) {
      console.error('转换失败:', error);
      this.setData({
        converting: false
      });
      wx.showToast({
        title: '转换失败，请重试',
        icon: 'none'
      });
    }
  },

  // 调用Stirling-PDF API的实际实现
  async callStirlingPDFAPI(formData) {
    return new Promise((resolve, reject) => {
      // 由于微信小程序的限制，需要通过服务器代理调用Stirling-PDF API
      // 这里展示一个模拟的实现

      // 实际项目中应该这样调用：
      wx.request({
        url: 'https://your-server.com/api/convert-images-to-pdf', // 你的服务器API
        method: 'POST',
        data: {
          images: this.data.selectedImages.map(img => ({
            path: img.path,
            name: img.name
          })),
          fitOption: this.data.fitOption,
          colorType: this.data.colorType,
          autoRotate: this.data.autoRotate
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve({
              success: true,
              fileName: res.data.fileName,
              fileSize: res.data.fileSize,
              previewUrl: res.data.previewUrl,
              downloadUrl: res.data.downloadUrl
            });
          } else {
            reject(new Error('服务器错误'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });

      // 模拟延迟和成功响应（用于演示）
      setTimeout(() => {
        resolve({
          success: true,
          fileName: 'converted_images.pdf',
          fileSize: '2.5 MB',
          previewUrl: '/images/pdf-icon.svg',
          downloadUrl: 'https://example.com/download/converted_images.pdf'
        });
      }, 2000);
    });
  },

  // 下载PDF
  onDownloadPDF() {
    wx.showToast({
      title: '开始下载PDF',
      icon: 'success'
    });
    // 实际实现需要调用下载接口
  },

  // 转换其他图片
  onConvertAnother() {
    this.setData({
      selectedImages: [],
      hasImages: false,
      converting: false,
      converted: false,
      progressPercent: 0,
      pdfPreviewUrl: '',
      pdfFileName: '',
      pdfFileSize: ''
    });
  }
});
