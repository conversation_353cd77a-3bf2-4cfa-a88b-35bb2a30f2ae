<!--pages/pdf-merge/pdf-merge.wxml-->
<!-- 自定义导航栏 -->
<view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
  <view class="navbar-content">
    <view class="navbar-back" bindtap="onBackClick" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx;">
      <image class="navbar-back-icon" src="/images/back.svg"></image>
    </view>
    <view class="navbar-title" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx; line-height: {{menuButtonHeightRpx}}rpx;">PDF文件合并</view>
  </view>
</view>

<!-- 页面内容 -->
<view class="container" style="padding-top: {{navbarHeight}}px;">

  <!-- 文件上传区域 -->
  <view class="upload-section" wx:if="{{selectedFiles.length === 0}}">
    <view class="upload-area" bindtap="onSelectFiles">
      <view class="upload-icon">
        <image class="upload-svg" src="/images/merge-pdf.svg"></image>
      </view>
      <view class="upload-content">
        <view class="upload-title">选择PDF文件</view>
        <view class="upload-subtitle">点击选择多个PDF文件进行合并</view>
        <view class="upload-tips">支持PDF格式，至少选择2个文件</view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content" wx:if="{{selectedFiles.length > 0 && !merging}}">
    <!-- 多文件显示 -->
    <view wx:for="{{selectedFiles}}" wx:key="id">
      <view class="file-info-card">
        <image class="file-icon" src="/images/merge-pdf.svg"></image>
        <view class="file-details">
          <view class="file-name">{{item.name}}</view>
          <view class="file-size-info">
            <text class="size-label">大小：</text>
            <text class="size-value">{{item.size}}</text>
          </view>
        </view>
        <view class="remove-btn" bindtap="onRemoveFile" data-index="{{index}}">移除</view>
      </view>
    </view>
  </view>



  <!-- 合并进行中 -->
  <view class="compressing-section" wx:if="{{merging}}">
    <view class="progress-card">
      <view class="progress-icon">
        <image class="progress-svg" src="/images/merge-pdf.svg"></image>
      </view>
      <view class="progress-content">
        <view class="progress-title">正在合并PDF文件...</view>
        <view class="progress-subtitle">请稍候，正在为您合并 {{selectedFiles.length}} 个文件</view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercent}}%"></view>
        </view>
        <view class="progress-text">{{progressPercent}}%</view>
      </view>
    </view>
  </view>

  <!-- 合并完成 -->
  <view class="result-section" wx:if="{{merged}}">
    <view class="result-card">
      <view class="result-icon">
        <image class="result-svg" src="/images/success.svg"></image>
      </view>
      <view class="result-content">
        <view class="result-title">合并完成！</view>
        <view class="result-subtitle">成功合并了 {{selectedFiles.length}} 个PDF文件</view>
        <view class="result-info">
          <view class="info-item">
            <text class="info-label">文件名：</text>
            <text class="info-value">{{mergedFileName}}.pdf</text>
          </view>
          <view class="info-item">
            <text class="info-label">文件大小：</text>
            <text class="info-value">{{mergedFileSize}}</text>
          </view>
        </view>

        <view class="result-actions">
          <view class="action-btn secondary" bindtap="onMergeAnother">
            <text>合并其他文件</text>
          </view>
          <view class="action-btn primary" bindtap="onDownloadPDF">
            <text>下载合并文件</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部胶囊按钮 -->
  <view class="bottom-capsule">
    <view class="capsule-btn left" bindtap="onSelectFiles">
      <view class="capsule-text">选择PDF</view>
    </view>
    <view class="capsule-btn right {{selectedFiles.length < 2 || merging ? 'disabled' : ''}}" bindtap="onStartMerge">
      <view class="capsule-text">{{merging ? '合并中...' : '合并PDF'}}</view>
    </view>
  </view>

</view>
