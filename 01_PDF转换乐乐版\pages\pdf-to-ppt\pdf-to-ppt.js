// pages/pdf-to-ppt/pdf-to-ppt.js
Page({
  data: {
    statusBarHeight: 0,
    navbarHeight: 0,
    menuButtonTopRpx: 0,
    menuButtonHeightRpx: 0,

    // 文件状态
    fileSelected: false,
    converting: false,
    converted: false,

    // 文件信息
    fileName: '',
    fileSize: '',
    filePath: '',

    // 多文件信息
    selectedFiles: [],

    // 转换选项（简化版，使用最佳默认值）
    outputFormat: 'pptx', // 固定为PPTX格式
    quality: 'high', // 固定为高质量
    imageResolution: 'high', // 固定为高分辨率

    // 页面范围（唯一可配置选项）
    pageRange: 'all', // all, custom
    pageRangeText: '全部页面',
    customPageRange: '',

    // 转换进度
    progressPercent: 0,

    // 转换结果
    originalSize: '',
    convertedSize: '',
    convertedFilePath: ''
  },

  onLoad(options) {
    this.initNavbar()
    // 页面加载后直接选择PDF文件
    setTimeout(() => {
      this.onSelectPDF()
    }, 500) // 延迟500ms确保页面完全加载
  },

  // 初始化导航栏
  initNavbar() {
    const systemInfo = wx.getSystemInfoSync()
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
    
    const statusBarHeight = systemInfo.statusBarHeight
    const menuButtonTop = menuButtonInfo.top
    const menuButtonHeight = menuButtonInfo.height
    const navbarHeight = statusBarHeight + menuButtonHeight + (menuButtonTop - statusBarHeight) * 2

    this.setData({
      statusBarHeight,
      navbarHeight,
      menuButtonTopRpx: (menuButtonTop - statusBarHeight) * 2,
      menuButtonHeightRpx: menuButtonHeight * 2
    })
  },

  // 返回按钮点击
  onBackClick() {
    wx.navigateBack()
  },



  onSelectPDF() {
    wx.showActionSheet({
      itemList: ['从文件选择', '从聊天记录选择'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 从文件选择
          this.onSelectFile()
        } else if (res.tapIndex === 1) {
          // 从聊天记录选择
          this.selectFromChat()
        }
      }
    })
  },

  // 从文件选择
  onSelectFile() {
    wx.chooseMedia({
      count: 9,
      mediaType: ['file'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 过滤出PDF文件
        const pdfFiles = res.tempFiles.filter(file =>
          file.tempFilePath.toLowerCase().endsWith('.pdf')
        )

        if (pdfFiles.length === 0) {
          wx.showToast({
            title: '请选择PDF文件',
            icon: 'none'
          })
          return
        }

        this.handleSelectedFiles(pdfFiles)
      },
      fail: (err) => {
        console.error('选择文件失败:', err)
      }
    })
  },

  // 从聊天记录选择
  selectFromChat() {
    wx.chooseMessageFile({
      count: 9,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        this.handleSelectedFiles(res.tempFiles)
      },
      fail: (err) => {
        console.error('选择文件失败:', err)
        wx.showToast({
          title: '选择文件失败',
          icon: 'none'
        })
      }
    })
  },

  // 处理选中的文件（支持多文件）
  handleSelectedFiles(files) {
    // 处理所有选择的文件
    const processedFiles = files.map((file, index) => ({
      id: Date.now() + index,
      name: file.name,
      size: this.formatFileSize(file.size),
      path: file.path,
      originalSize: file.size,
      convertedSize: null,
      converted: false
    }))

    this.setData({
      selectedFiles: processedFiles,
      fileSelected: true,
      converting: false,
      converted: false,
      progressPercent: 0,
      convertedSize: '',
      convertedFilePath: ''
    })

    // 如果只选择了一个文件，保持原有的单文件显示逻辑
    if (files.length === 1) {
      this.handleSelectedFile(files[0])
    }
  },

  // 处理选中的单个文件（保持向后兼容）
  handleSelectedFile(file) {
    this.setData({
      fileSelected: true,
      fileName: file.name,
      fileSize: this.formatFileSize(file.size),
      filePath: file.path,
      converting: false,
      converted: false,
      progressPercent: 0,
      convertedSize: '',
      convertedFilePath: ''
    })

    // 预估转换后文件大小
    this.estimateConvertedSize(file.size)
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 预估转换后文件大小
  estimateConvertedSize(originalBytes) {
    // PPT文件通常比PDF大，因为包含更多格式信息
    // 使用高质量和高分辨率的固定倍数
    const multiplier = 2.3 // 高质量 + 高分辨率的倍数

    const estimatedBytes = originalBytes * multiplier
    this.setData({
      convertedSize: this.formatFileSize(estimatedBytes)
    })
  },



  // 页面范围变化
  onPageRangeChange(e) {
    const range = e.currentTarget.dataset.range
    let rangeText = ''
    
    switch(range) {
      case 'all':
        rangeText = '全部页面'
        break
      case 'custom':
        rangeText = '自定义范围'
        break
    }
    
    this.setData({
      pageRange: range,
      pageRangeText: rangeText
    })
  },

  // 自定义页面范围输入
  onCustomPageRangeInput(e) {
    this.setData({
      customPageRange: e.detail.value
    })
  },



  // 解析文件大小字符串为字节数
  parseFileSize(sizeStr) {
    const units = { 'B': 1, 'KB': 1024, 'MB': 1024*1024, 'GB': 1024*1024*1024 }
    const match = sizeStr.match(/^([\d.]+)\s*([A-Z]+)$/)
    if (match) {
      return parseFloat(match[1]) * (units[match[2]] || 1)
    }
    return 0
  },

  // 移除单个文件
  onRemoveFile(e) {
    const fileId = e.currentTarget.dataset.id
    if (fileId) {
      // 移除特定文件
      const updatedFiles = this.data.selectedFiles.filter(file => file.id !== fileId)
      this.setData({
        selectedFiles: updatedFiles,
        fileSelected: updatedFiles.length > 0
      })

      // 如果没有文件了，重置状态
      if (updatedFiles.length === 0) {
        this.resetAllStates()
      }
    } else {
      // 移除所有文件
      this.resetAllStates()
    }
  },

  // 重置所有状态
  resetAllStates() {
    this.setData({
      fileSelected: false,
      converting: false,
      converted: false,
      fileName: '',
      fileSize: '',
      filePath: '',
      selectedFiles: [],
      progressPercent: 0,
      convertedSize: '',
      convertedFilePath: '',
      pageRange: 'all',
      pageRangeText: '全部页面',
      customPageRange: ''
    })
  },

  // 开始转换
  onStartConversion() {
    if (!this.data.fileSelected || this.data.converting) {
      return
    }

    // 验证自定义页面范围
    if (this.data.pageRange === 'custom' && !this.data.customPageRange.trim()) {
      wx.showToast({
        title: '请输入页面范围',
        icon: 'none'
      })
      return
    }

    this.setData({
      converting: true,
      converted: false,
      progressPercent: 0
    })

    // 模拟转换进度
    this.simulateConversion()
  },

  // 模拟转换过程
  simulateConversion() {
    let progress = 0
    const interval = setInterval(() => {
      progress += Math.random() * 15 + 5
      if (progress >= 100) {
        progress = 100
        clearInterval(interval)
        this.completeConversion()
      }
      this.setData({
        progressPercent: Math.floor(progress)
      })
    }, 500)
  },

  // 完成转换
  completeConversion() {
    // 更新多文件的转换状态
    const updatedFiles = this.data.selectedFiles.map(file => ({
      ...file,
      converted: true,
      convertedSize: this.formatFileSize(file.originalSize * 2.3) // PPT通常比PDF大
    }))

    // 生成转换后的文件大小
    const convertedSize = this.generateConvertedSize()

    this.setData({
      converting: false,
      converted: true,
      selectedFiles: updatedFiles,
      originalSize: this.data.fileSize,
      convertedSize: convertedSize,
      convertedFilePath: this.data.filePath // 实际应用中这里应该是转换后的文件路径
    })

    wx.showToast({
      title: '转换完成！',
      icon: 'success'
    })
  },

  // 生成转换后的文件大小（模拟）
  generateConvertedSize() {
    // 使用之前估算的大小
    return this.data.convertedSize
  },

  // 下载PPT文件
  onDownloadPPT() {
    if (!this.data.converted) {
      wx.showToast({
        title: '请先完成PDF转换',
        icon: 'none'
      })
      return
    }

    wx.showToast({
      title: '开始下载PPT文件',
      icon: 'success'
    })

    // 这里应该实现真实的下载逻辑
    console.log('下载文件:', this.data.convertedFilePath)
  },

  // 转换其他文件
  onConvertAnother() {
    this.resetAllStates()
  }
})
