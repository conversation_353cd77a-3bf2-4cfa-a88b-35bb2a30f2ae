/* pages/pdf-unlock/pdf-unlock.wxss */

/* 自定义导航栏样式 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
  position: relative;
}

.navbar-back {
  position: absolute;
  left: 32rpx;
  width: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.navbar-back:active {
  opacity: 0.6;
  transform: scale(0.9);
}

.navbar-back-icon {
  width: 40rpx;
  height: 40rpx;
}

.navbar-title {
  position: absolute;
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 32rpx;
  margin-top: 30rpx;
}

/* 文件上传区域 */
.upload-section {
  margin-bottom: 48rpx;
}

.upload-area {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 80rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  border: 4rpx dashed rgba(163, 177, 198, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* 新拟物设计阴影 */
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.upload-area:active {
  transform: scale(0.98);
  box-shadow:
    inset 6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    inset -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.upload-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(255, 165, 0, 0.3),
    0 0 8rpx rgba(255, 165, 0, 0.2);
}

.upload-svg {
  width: 80rpx;
  height: 80rpx;
  opacity: 0.8;
}

.upload-content {
  flex: 1;
}

.upload-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.upload-subtitle {
  font-size: 28rpx;
  color: #5a6c7d;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.upload-tips {
  font-size: 24rpx;
  color: #8a9aa9;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

/* 文件信息区域 */
.file-info-card {
  background: #f0f2f5;
  border-radius: 24rpx;
  padding: 30rpx 52rpx;
  margin: 0 -32rpx 32rpx -32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  box-shadow:
    8rpx 8rpx 16rpx rgba(163, 177, 198, 0.4),
    -8rpx -8rpx 16rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
}

.file-icon {
  width: 100rpx;
  height: 100rpx;
  flex-shrink: 0;
  padding: 12rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(163, 177, 198, 0.3);
}

.file-details {
  flex: 1;
}

.file-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.file-status-info {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  flex-wrap: wrap;
}

.status-label {
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.status-separator {
  margin: 0 16rpx;
}

.status-value {
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.status-value.unlocked {
  color: #27ae60;
}

.status-value.locked {
  color: #e74c3c;
}

.size-label {
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.size-value {
  color: #5a6c7d;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}



/* 主要内容区域 */
.main-content {
  padding: 30rpx;
  padding-bottom: 260rpx; /* 为底部固定区域预留空间 */
}



/* 解锁进行中 */
.unlocking-section {
  margin-bottom: 48rpx;
}

.progress-card {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.progress-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(255, 165, 0, 0.3),
    0 0 8rpx rgba(255, 165, 0, 0.2);
}

.progress-svg {
  width: 80rpx;
  height: 80rpx;
  opacity: 0.8;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.progress-content {
  width: 100%;
}

.progress-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.progress-subtitle {
  font-size: 26rpx;
  color: #5a6c7d;
  margin-bottom: 40rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

.progress-bar {
  width: 100%;
  height: 12rpx;
  background: rgba(163, 177, 198, 0.3);
  border-radius: 6rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.4),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #FF8C00 0%, #FFA500 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
  box-shadow: 0 0 8rpx rgba(255, 140, 0, 0.3);
}

.progress-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF8C00;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

/* 解锁结果 */
.result-section {
  margin-bottom: 48rpx;
}

.result-card {
  background: #f0f2f5;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.6),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.result-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  padding: 16rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(255, 255, 255, 1),
    inset -2rpx -2rpx 4rpx rgba(50, 205, 50, 0.3),
    0 0 8rpx rgba(50, 205, 50, 0.2);
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #27ae60;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.result-subtitle {
  font-size: 26rpx;
  color: #5a6c7d;
  margin-bottom: 32rpx;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.6);
}

/* 结果操作按钮 */
.result-actions {
  display: flex;
  gap: 24rpx;
  justify-content: center;
}

.action-btn.secondary {
  background: #f0f2f5;
  color: #5a6c7d;
  padding: 20rpx 40rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow:
    6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8);
}

.action-btn.primary {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow:
    6rpx 6rpx 12rpx rgba(39, 174, 96, 0.3),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
}

/* 底部胶囊按钮容器 */
.bottom-capsule {
  position: fixed;
  bottom: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 24rpx;
  z-index: 1000;
}

.capsule-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 17rpx 48rpx;
  border-radius: 42rpx;
  box-shadow:
    0 6rpx 24rpx rgba(0, 0, 0, 0.12),
    0 2rpx 6rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  min-width: 240rpx;
  height: 55rpx;
}

.capsule-btn:active {
  transform: scale(0.96);
  box-shadow:
    0 2rpx 8rpx rgba(0, 0, 0, 0.16),
    0 1rpx 2rpx rgba(0, 0, 0, 0.12);
}

.capsule-btn.left {
  background: linear-gradient(135deg, #FF8C00 0%, #FFA500 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(255, 140, 0, 0.3),
    0 2rpx 6rpx rgba(255, 140, 0, 0.2);
}

.capsule-btn.right {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
  box-shadow:
    0 6rpx 24rpx rgba(39, 174, 96, 0.3),
    0 2rpx 6rpx rgba(39, 174, 96, 0.2);
}

.capsule-btn.disabled {
  background: linear-gradient(135deg, #CCCCCC 0%, #999999 100%);
  color: rgba(255, 255, 255, 0.7);
  pointer-events: none;
  box-shadow:
    0 6rpx 24rpx rgba(0, 0, 0, 0.12),
    0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.capsule-text {
  font-size: 39rpx;
  font-weight: 500;
  line-height: 1;
}

/* 密码输入弹窗 */
.password-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(163, 177, 198, 0.3);
  backdrop-filter: blur(20rpx);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
  animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(20rpx);
  }
}

.modal-content {
  background: rgba(240, 242, 245, 0.95);
  backdrop-filter: blur(30rpx);
  border-radius: 32rpx;
  width: 100%;
  max-width: 600rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow:
    12rpx 12rpx 24rpx rgba(163, 177, 198, 0.4),
    -12rpx -12rpx 24rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2);
  animation: slideUp 0.4s ease-out;
  overflow: hidden;
}

@keyframes slideUp {
  from {
    transform: translateY(80rpx) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.modal-header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  padding: 40rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  border-bottom: 1rpx solid rgba(163, 177, 198, 0.2);
  box-shadow:
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8),
    0 1rpx 3rpx rgba(163, 177, 198, 0.1);
}

.modal-title {
  color: #2c3e50;
  font-size: 32rpx;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow:
    4rpx 4rpx 8rpx rgba(163, 177, 198, 0.3),
    -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.modal-close:active {
  transform: scale(0.95);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.3),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.close-icon {
  color: #5a6c7d;
  font-size: 28rpx;
  font-weight: bold;
}

.modal-body {
  padding: 48rpx 32rpx;
  background: rgba(255, 255, 255, 0.4);
}

.input-section {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.input-label {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}

.input-container {
  position: relative;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  box-shadow:
    inset 4rpx 4rpx 8rpx rgba(163, 177, 198, 0.2),
    inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.9);
}

.input-container:focus-within {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 140, 0, 0.3);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.1),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 1),
    0 0 0 4rpx rgba(255, 140, 0, 0.1);
}

.password-input {
  width: 100%;
  padding: 28rpx 140rpx 28rpx 28rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #2c3e50;
  background: transparent;
  border: none;
  outline: none;
}

.password-toggle {
  position: absolute;
  right: 28rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.2),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.password-toggle:active {
  transform: translateY(-50%) scale(0.95);
  box-shadow:
    inset 4rpx 4rpx 8rpx rgba(163, 177, 198, 0.3),
    inset -4rpx -4rpx 8rpx rgba(255, 255, 255, 0.9);
}

.toggle-icon {
  font-size: 24rpx;
}

.input-tip {
  font-size: 26rpx;
  color: #5a6c7d;
  line-height: 1.5;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  box-shadow:
    inset 2rpx 2rpx 4rpx rgba(163, 177, 198, 0.1),
    inset -2rpx -2rpx 4rpx rgba(255, 255, 255, 0.8);
}

.modal-footer {
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid rgba(163, 177, 198, 0.2);
  display: flex;
  gap: 24rpx;
  box-shadow:
    inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.modal-btn:active {
  transform: scale(0.96);
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.8);
  color: #5a6c7d;
  box-shadow:
    6rpx 6rpx 12rpx rgba(163, 177, 198, 0.3),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.3);
}

.cancel-btn:active {
  box-shadow:
    inset 3rpx 3rpx 6rpx rgba(163, 177, 198, 0.3),
    inset -3rpx -3rpx 6rpx rgba(255, 255, 255, 0.8);
}

.confirm-btn {
  background: rgba(255, 140, 0, 0.9);
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  box-shadow:
    6rpx 6rpx 12rpx rgba(163, 177, 198, 0.4),
    -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.8),
    inset 0 0 0 1rpx rgba(255, 255, 255, 0.2),
    0 0 20rpx rgba(255, 140, 0, 0.3);
}

.confirm-btn:active {
  box-shadow:
    inset 3rpx 3rpx 6rpx rgba(163, 177, 198, 0.4),
    inset -3rpx -3rpx 6rpx rgba(255, 255, 255, 0.8),
    0 0 15rpx rgba(255, 140, 0, 0.4);
}
