<!--pages/compress/compress.wxml-->
<!-- 自定义导航栏 -->
<view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
  <view class="navbar-content">
    <view class="navbar-back" bindtap="onBackClick" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx;">
      <image class="navbar-back-icon" src="/images/back.svg"></image>
    </view>
    <view class="navbar-title" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx; line-height: {{menuButtonHeightRpx}}rpx;">PDF智能压缩</view>
  </view>
</view>

<!-- 自定义目标大小设置弹窗 -->
<view class="target-size-modal" wx:if="{{showTargetSizeModal}}" bindtap="hideTargetSizeModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">⚙️ 设置目标文件大小</text>
      <view class="modal-close" bindtap="hideTargetSizeModal">
        <text class="close-icon">✕</text>
      </view>
    </view>

    <view class="modal-body">
      <view class="input-section">
        <text class="input-label">期望压缩后的文件大小</text>
        <view class="input-container">
          <input
            class="size-input"
            placeholder="例如：10MB、5MB、2GB"
            value="{{inputTargetSize}}"
            bindinput="onTargetSizeInput"
            focus="{{inputFocus}}"
          />
          <text class="input-unit">单位</text>
        </view>
        <text class="input-tip">💡 支持格式：10MB、5.5MB、2GB、500KB 或简写 10M、5G、500K（大小写均可）</text>
      </view>
    </view>

    <view class="modal-footer">
      <view class="modal-btn cancel-btn" bindtap="hideTargetSizeModal">
        <text>取消</text>
      </view>
      <view class="modal-btn confirm-btn" bindtap="confirmTargetSize">
        <text>确定</text>
      </view>
    </view>
  </view>
</view>

<!-- 页面内容 -->
<view class="container" style="padding-top: {{navbarHeight}}px;">
  
  <!-- 文件上传区域 -->
  <view class="upload-section" wx:if="{{!fileSelected}}">
    <view class="upload-area" bindtap="onSelectFile">
      <view class="upload-icon">
        <image class="upload-svg" src="/images/compress.svg"></image>
      </view>
      <view class="upload-content">
        <view class="upload-title">选择PDF文件</view>
        <view class="upload-subtitle">点击选择或拖拽文件到此处</view>
        <view class="upload-tips">支持PDF格式</view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content" wx:if="{{fileSelected && !compressing}}">
    <!-- 多文件显示 -->
    <view wx:if="{{selectedFiles.length > 1}}">
      <view class="file-info-card" wx:for="{{selectedFiles}}" wx:key="id">
        <image class="file-icon" src="/images/pdf-icon.svg"></image>
        <view class="file-details">
          <view class="file-name">{{item.name}}</view>
          <view class="file-size-info">
            <text class="size-label">压缩前：</text>
            <text class="size-value">{{item.size}}</text>
            <text class="size-separator">　</text>
            <text class="size-label">压缩后：</text>
            <text class="size-value {{item.compressedSize ? 'compressed' : 'placeholder'}}">{{item.compressedSize || 'XXX KB'}}</text>
          </view>
        </view>
        <view class="remove-btn" bindtap="onSettings">设置</view>
      </view>
    </view>

    <!-- 单文件显示（保持原有逻辑） -->
    <view wx:if="{{selectedFiles.length <= 1}}">
      <view class="file-info-card">
        <image class="file-icon" src="/images/pdf-icon.svg"></image>
        <view class="file-details">
          <view class="file-name">{{fileName}}</view>
          <view class="file-size-info">
            <text class="size-label">压缩前：</text>
            <text class="size-value">{{fileSize}}</text>
            <text class="size-separator">　</text>
            <text class="size-label">压缩后：</text>
            <text class="size-value {{compressedSize ? 'compressed' : 'placeholder'}}">{{compressedSize || 'XXX KB'}}</text>
          </view>
        </view>
        <view class="remove-btn" bindtap="onSettings">设置</view>
      </view>
    </view>

  </view>

  <!-- 底部固定区域 -->
  <view class="footer-area" wx:if="{{fileSelected && !compressing && !compressed}}">
    <!-- 压缩设置 -->
    <view class="compress-settings">


      <!-- 压缩强度滑块 -->
      <view class="setting-item">
        <view class="setting-header">
          <text class="setting-label">压缩强度</text>
          <text class="setting-value">{{compressionLevel}}</text>
        </view>
        <view class="slider-container">
          <slider
            value="{{compressionLevel}}"
            min="1"
            max="9"
            step="1"
            show-value="false"
            bindchange="onCompressionLevelChange"
            activeColor="#DC143C"
            backgroundColor="rgba(220, 20, 60, 0.1)"
            block-color="#ffffff"
            block-size="28"
          />
        </view>
      </view>
    </view>
  </view>

  <!-- 压缩进行中 -->
  <view class="compressing-section" wx:if="{{compressing}}">
    <view class="progress-card">
      <view class="progress-icon">
        <image class="progress-svg" src="/images/compress.svg"></image>
      </view>
      <view class="progress-content">
        <view class="progress-title">正在压缩PDF...</view>
        <view class="progress-subtitle">请稍候，正在为您优化文件大小</view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercent}}%"></view>
        </view>
        <view class="progress-text">{{progressPercent}}%</view>
      </view>
    </view>
  </view>

  <!-- 压缩完成 -->
  <view class="result-section" wx:if="{{compressed}}">
    <view class="result-card">
      <view class="result-icon">
        <image class="result-svg" src="/images/success.svg"></image>
      </view>
      <view class="result-content">
        <view class="result-title">压缩完成！</view>
        <view class="result-subtitle">节省了 {{savedSize}}，压缩率 {{compressionRatio}}%</view>

        <view class="result-actions">
          <view class="action-btn secondary" bindtap="onCompressAnother">
            <text>压缩其他文件</text>
          </view>
          <view class="action-btn primary" bindtap="onDownloadPDF">
            <text>下载压缩文件</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部胶囊按钮 -->
  <view class="bottom-capsule">
    <view class="capsule-btn left" bindtap="onSelectPDF">
      <view class="capsule-text">选择PDF</view>
    </view>
    <view class="capsule-btn right {{!compressed ? 'disabled' : ''}}" bindtap="onDownloadPDF">
      <view class="capsule-text">下载PDF</view>
    </view>
  </view>

</view>
