// pages/pdf-to-excel/pdf-to-excel.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 导航栏相关
    statusBarHeight: 0,
    navbarHeight: 0,
    menuButtonTopRpx: 0,
    menuButtonHeightRpx: 0,
    
    // 文件相关
    fileSelected: false,
    fileName: '',
    fileSize: '',
    filePath: '',
    selectedFiles: [], // 多文件选择
    
    // 转换设置 - 默认表格模式（中敏感度）
    conversionMode: 'table', // 固定为表格模式
    tableSensitivity: 'medium', // 表格检测敏感度：high/medium/low
    includeHeaders: true, // 是否包含表头
    mergedCellHandling: 'split_fill', // 合并单元格处理：keep_merged/split_fill/split_empty
    pageRange: 'all', // all: 全部页面, custom: 自定义
    customPageRange: '',
    
    // 转换状态
    converting: false,
    converted: false,
    progressPercent: 0,
    
    // 转换结果
    convertedFileName: '',
    convertedFileSize: '',
    convertedFilePath: '',
    convertedSize: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initNavbar();
    // 页面加载后直接选择PDF文件
    setTimeout(() => {
      this.showSelectOptions()
    }, 500) // 延迟500ms确保页面完全加载
  },

  /**
   * 初始化导航栏
   */
  initNavbar() {
    const systemInfo = wx.getSystemInfoSync();
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      navbarHeight: systemInfo.statusBarHeight + 44,
      menuButtonTopRpx: (menuButtonInfo.top - systemInfo.statusBarHeight) * 2,
      menuButtonHeightRpx: menuButtonInfo.height * 2
    });
  },

  /**
   * 返回按钮点击
   */
  onBackClick() {
    wx.navigateBack();
  },

  /**
   * 选择PDF文件 - 上传区域点击
   */
  onSelectPDF() {
    this.showSelectOptions();
  },

  /**
   * 显示选择选项
   */
  showSelectOptions() {
    wx.showActionSheet({
      itemList: ['从文件选择', '从聊天记录选择'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 从文件选择
          this.selectFromFile();
        } else if (res.tapIndex === 1) {
          // 从聊天记录选择
          this.selectFromChat();
        }
      }
    });
  },

  /**
   * 从文件选择
   */
  selectFromFile() {
    wx.chooseMessageFile({
      count: 9,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        // 处理所有选择的文件
        const files = res.tempFiles.map((file, index) => ({
          id: Date.now() + index,
          name: file.name,
          size: this.formatFileSize(file.size),
          path: file.path,
          originalSize: file.size
        }));

        this.setData({
          selectedFiles: files,
          fileSelected: true
        });

        // 如果只选择了一个文件，保持原有的单文件显示逻辑
        if (files.length === 1) {
          this.setData({
            fileName: files[0].name,
            fileSize: files[0].size,
            filePath: files[0].path
          });
        }

        console.log('选择的文件:', files);

        wx.showToast({
          title: `已选择${files.length}个文件`,
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('选择文件失败:', err);
        wx.showToast({
          title: '选择文件失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 从聊天记录选择
   */
  selectFromChat() {
    wx.chooseMessageFile({
      count: 9,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        // 处理所有选择的文件
        const files = res.tempFiles.map((file, index) => ({
          id: Date.now() + index,
          name: file.name,
          size: this.formatFileSize(file.size),
          path: file.path,
          originalSize: file.size
        }));

        this.setData({
          selectedFiles: files,
          fileSelected: true
        });

        // 如果只选择了一个文件，保持原有的单文件显示逻辑
        if (files.length === 1) {
          this.setData({
            fileName: files[0].name,
            fileSize: files[0].size,
            filePath: files[0].path
          });
        }

        console.log('从聊天记录选择的文件:', files);

        wx.showToast({
          title: `已选择${files.length}个文件`,
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('选择文件失败:', err);
        wx.showToast({
          title: '选择文件失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 设置按钮点击
   */
  onSettings() {
    // 可以在这里添加更多设置选项
    wx.showToast({
      title: '设置功能',
      icon: 'none'
    });
  },

  /**
   * 移除文件
   */
  onRemoveFile() {
    this.setData({
      fileSelected: false,
      fileName: '',
      fileSize: '',
      filePath: '',
      converted: false,
      converting: false,
      progressPercent: 0
    });
  },



  /**
   * 页面范围改变
   */
  onPageRangeChange(e) {
    const range = e.currentTarget.dataset.range;
    this.setData({
      pageRange: range
    });
  },

  /**
   * 自定义页面范围输入
   */
  onCustomRangeInput(e) {
    this.setData({
      customPageRange: e.detail.value
    });
  },

  /**
   * 下载Excel文件
   */
  onDownloadExcel() {
    if (!this.data.converted) {
      return;
    }

    wx.showLoading({
      title: '准备下载...'
    });

    // 模拟下载过程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '下载完成',
        icon: 'success'
      });
    }, 1500);
  },

  /**
   * 转换其他文件
   */
  onConvertAnother() {
    this.setData({
      fileSelected: false,
      fileName: '',
      fileSize: '',
      filePath: '',
      converted: false,
      converting: false,
      progressPercent: 0,
      conversionMode: 'table',
      pageRange: 'all',
      customPageRange: ''
    });
  },

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

});
