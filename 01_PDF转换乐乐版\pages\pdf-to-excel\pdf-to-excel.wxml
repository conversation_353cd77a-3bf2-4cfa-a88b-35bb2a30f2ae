<!--pages/pdf-to-excel/pdf-to-excel.wxml-->
<!-- 自定义导航栏 -->
<view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
  <view class="navbar-content">
    <view class="navbar-back" bindtap="onBackClick" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx;">
      <image class="navbar-back-icon" src="/images/back.svg"></image>
    </view>
    <view class="navbar-title" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx; line-height: {{menuButtonHeightRpx}}rpx;">PDF转Excel</view>
  </view>
</view>

<!-- 页面内容 -->
<view class="container" style="padding-top: {{navbarHeight}}px;">
  
  <!-- 文件上传区域 -->
  <view class="upload-section" wx:if="{{!fileSelected}}">
    <view class="upload-area" bindtap="onSelectPDF">
      <view class="upload-icon">
        <image class="upload-svg" src="/images/pdf-to-excel.svg"></image>
      </view>
      <view class="upload-content">
        <view class="upload-title">选择PDF文件</view>
        <view class="upload-subtitle">点击选择或拖拽文件到此处</view>
        <view class="upload-tips">支持PDF格式，将转换为Excel表格</view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content" wx:if="{{fileSelected && !converting}}">
    <!-- 多文件显示 -->
    <view wx:if="{{selectedFiles.length > 1}}">
      <view class="file-info-card" wx:for="{{selectedFiles}}" wx:key="id">
        <image class="file-icon" src="/images/pdf-to-excel.svg"></image>
        <view class="file-details">
          <view class="file-name">{{item.name}}</view>
          <view class="file-size-info">
            <text class="size-label">转换前：</text>
            <text class="size-value">{{item.size}}</text>
            <text class="size-separator">　</text>
            <text class="size-label">转换后：</text>
            <text class="size-value {{convertedSize ? 'converted' : 'placeholder'}}">{{convertedSize || 'XXX KB'}}</text>
          </view>
        </view>
        <view class="remove-btn" bindtap="onSettings">设置</view>
      </view>
    </view>

    <!-- 单文件显示（保持原有逻辑） -->
    <view wx:if="{{selectedFiles.length <= 1}}">
      <view class="file-info-card">
        <image class="file-icon" src="/images/pdf-to-excel.svg"></image>
        <view class="file-details">
          <view class="file-name">{{fileName}}</view>
          <view class="file-size-info">
            <text class="size-label">转换前：</text>
            <text class="size-value">{{fileSize}}</text>
            <text class="size-separator">　</text>
            <text class="size-label">转换后：</text>
            <text class="size-value placeholder">XXX KB</text>
          </view>
        </view>
        <view class="remove-btn" bindtap="onSettings">设置</view>
      </view>
    </view>
  </view>

  <!-- 底部固定区域 -->
  <view class="footer-area" wx:if="{{fileSelected && !converting && !converted}}">
    <!-- 转换设置 -->
    <view class="convert-settings">
      <!-- 页面范围 -->
      <view class="setting-item">
        <view class="setting-header">
          <text class="setting-label">页面范围</text>
          <text class="setting-value">{{pageRange === 'all' ? '全部页面' : '自定义'}}</text>
        </view>
        <view class="range-selector">
          <view class="range-btn {{pageRange === 'all' ? 'active' : ''}}" bindtap="onPageRangeChange" data-range="all">
            <text>全部</text>
          </view>
          <view class="range-btn {{pageRange === 'custom' ? 'active' : ''}}" bindtap="onPageRangeChange" data-range="custom">
            <text>自定义</text>
          </view>
        </view>
        <view class="custom-range" wx:if="{{pageRange === 'custom'}}">
          <input class="range-input" placeholder="例如：1-5,8,10-12" value="{{customPageRange}}" bindinput="onCustomRangeInput" />
        </view>
      </view>
    </view>
  </view>

  <!-- 转换进行中 -->
  <view class="compressing-section" wx:if="{{converting}}">
    <view class="progress-card">
      <view class="progress-icon">
        <image class="progress-svg" src="/images/pdf-to-excel.svg"></image>
      </view>
      <view class="progress-content">
        <view class="progress-title">正在转换为Excel...</view>
        <view class="progress-subtitle">请稍候，正在为您转换PDF文件</view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercent}}%"></view>
        </view>
        <view class="progress-text">{{progressPercent}}%</view>
      </view>
    </view>
  </view>

  <!-- 转换完成 -->
  <view class="result-section" wx:if="{{converted}}">
    <view class="result-card">
      <view class="result-icon">
        <image class="result-svg" src="/images/success.svg"></image>
      </view>
      <view class="result-content">
        <view class="result-title">转换完成！</view>
        <view class="result-subtitle">PDF已成功转换为Excel格式</view>
        <view class="result-info">
          <view class="info-item">
            <text class="info-label">文件名：</text>
            <text class="info-value">{{convertedFileName}}.xlsx</text>
          </view>
          <view class="info-item">
            <text class="info-label">文件大小：</text>
            <text class="info-value">{{convertedFileSize}}</text>
          </view>
          <view class="info-item">
            <text class="info-label">转换模式：</text>
            <text class="info-value">{{conversionMode === 'table' ? '表格模式' : '文本模式'}}</text>
          </view>
        </view>

        <view class="result-actions">
          <view class="action-btn secondary" bindtap="onConvertAnother">
            <text>转换其他文件</text>
          </view>
          <view class="action-btn primary" bindtap="onDownloadExcel">
            <text>下载Excel文件</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部胶囊按钮 -->
  <view class="bottom-capsule">
    <view class="capsule-btn left" bindtap="showSelectOptions">
      <view class="capsule-text">选择PDF</view>
    </view>
    <view class="capsule-btn right {{!converted ? 'disabled' : ''}}" bindtap="onDownloadExcel">
      <view class="capsule-text">下载Excel</view>
    </view>
  </view>

</view>
