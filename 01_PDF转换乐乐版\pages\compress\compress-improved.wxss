/* 更符合Stirling-PDF特性的压缩页面样式 */

.container {
  padding: 20rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* 导航栏 */
.nav-bar {
  text-align: center;
  margin-bottom: 40rpx;
}

.nav-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.nav-subtitle {
  font-size: 28rpx;
  color: #7f8c8d;
}

/* 文件选择区域 */
.file-select-section {
  margin-bottom: 40rpx;
}

.select-area {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  border: 4rpx dashed #e0e6ed;
  margin-bottom: 30rpx;
}

.select-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.select-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.select-desc {
  font-size: 28rpx;
  color: #7f8c8d;
  margin-bottom: 20rpx;
}

.select-limit {
  font-size: 24rpx;
  color: #3498db;
  margin-bottom: 10rpx;
}

.select-note {
  font-size: 24rpx;
  color: #95a5a6;
}

.link {
  color: #e74c3c;
  text-decoration: underline;
}

.select-buttons {
  display: flex;
  gap: 20rpx;
}

.btn-primary, .btn-secondary {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.btn-secondary {
  background: white;
  color: #3498db;
  border: 2rpx solid #3498db;
}

/* 配置区域 */
.config-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
}

.file-info {
  background: #ecf0f1;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.file-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.file-size {
  font-size: 26rpx;
  color: #7f8c8d;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 30rpx;
}

/* 压缩级别选择 */
.compress-level {
  margin-bottom: 40rpx;
}

.level-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.level-item {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 15rpx;
  padding: 30rpx;
  transition: all 0.3s;
}

.level-item.active {
  background: #e3f2fd;
  border-color: #2196f3;
}

.level-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.level-desc {
  font-size: 26rpx;
  color: #7f8c8d;
  margin-bottom: 10rpx;
}

.level-range {
  font-size: 24rpx;
  color: #3498db;
}

/* 高级选项 */
.advanced-options {
  margin-bottom: 40rpx;
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #ecf0f1;
}

.option-item:last-child {
  border-bottom: none;
}

.option-info {
  flex: 1;
}

.option-name {
  font-size: 30rpx;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #7f8c8d;
}

.target-size {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.target-size input {
  width: 120rpx;
  height: 60rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 10rpx;
  text-align: center;
  font-size: 28rpx;
}

/* 预估效果 */
.estimate-section {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.estimate-info {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.estimate-item {
  display: flex;
  justify-content: space-between;
}

.label {
  color: #7f8c8d;
  font-size: 26rpx;
}

.value {
  color: #2c3e50;
  font-size: 26rpx;
  font-weight: bold;
}

.value.success {
  color: #27ae60;
}

/* 压缩按钮 */
.action-section {
  text-align: center;
}

.btn-compress {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

/* 处理进度 */
.processing-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
  margin-bottom: 30rpx;
}

.process-info {
  margin-bottom: 40rpx;
}

.process-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
}

.process-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
}

.process-desc {
  font-size: 28rpx;
  color: #7f8c8d;
}

.progress-bar {
  width: 100%;
  height: 20rpx;
  background: #ecf0f1;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  transition: width 0.3s;
}

.progress-text {
  font-size: 28rpx;
  color: #3498db;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.process-details {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #7f8c8d;
  font-size: 26rpx;
}

.detail-value {
  color: #2c3e50;
  font-size: 26rpx;
  font-weight: bold;
}

.btn-cancel {
  width: 200rpx;
  height: 70rpx;
  background: #e74c3c;
  color: white;
  border-radius: 35rpx;
  font-size: 26rpx;
  border: none;
}

/* 结果展示 */
.result-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
}

.result-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.result-icon {
  font-size: 100rpx;
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #27ae60;
  margin-bottom: 10rpx;
}

.result-desc {
  font-size: 28rpx;
  color: #7f8c8d;
}

.result-comparison {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.comparison-item {
  text-align: center;
  flex: 1;
}

.comparison-label {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 10rpx;
}

.comparison-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #2c3e50;
}

.comparison-arrow {
  font-size: 40rpx;
  color: #3498db;
  margin: 0 20rpx;
}

.result-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #27ae60;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #7f8c8d;
}

.result-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.btn-download, .btn-share, .btn-restart {
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
}

.btn-download {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
}

.btn-share {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.btn-restart {
  background: white;
  color: #3498db;
  border: 2rpx solid #3498db;
}

/* 功能说明 */
.features-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.feature-icon {
  font-size: 40rpx;
}

.feature-text {
  font-size: 28rpx;
  color: #2c3e50;
}
