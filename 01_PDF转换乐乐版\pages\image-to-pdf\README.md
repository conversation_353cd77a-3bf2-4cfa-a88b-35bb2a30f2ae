# 图片转PDF页面

## 功能概述
将多张图片转换为单个PDF文件的功能页面。

## 主要功能

### 1. 图片选择
- 支持从相册或相机选择图片
- 最多可选择9张图片
- 支持JPG、PNG、GIF等常见格式
- 可以添加更多图片或移除已选图片

### 2. 图片管理
- 显示图片预览和基本信息
- 支持调整图片顺序（上移/下移）
- 显示图片序号和文件大小

### 3. 转换设置
- **页面适配选项**：
  - 文档适配图片：PDF页面大小适配图片尺寸
  - 图片适配文档：图片缩放适配标准页面
  - 保持宽高比：保持图片原始比例
- **颜色类型**：
  - 彩色：保持原始颜色
  - 灰度：转换为灰度图
  - 黑白：转换为黑白图
- **自动旋转**：自动旋转图片以适应页面方向

### 4. 转换过程
- 实时显示转换进度
- 转换状态提示
- 错误处理和重试机制

### 5. 结果展示
- PDF预览
- 文件信息显示
- 下载功能

## API集成

### Stirling-PDF API参数映射
- `fileInput`: 选择的图片文件数组
- `fitOption`: 页面适配选项
- `colorType`: 颜色类型
- `autoRotate`: 自动旋转开关

### 请求示例
```javascript
// 调用Stirling-PDF API
const formData = new FormData();
selectedImages.forEach(image => {
  formData.append('fileInput', image.file);
});
formData.append('fitOption', fitOption);
formData.append('colorType', colorType);
formData.append('autoRotate', autoRotate);

fetch('/api/v1/convert/img/pdf', {
  method: 'POST',
  body: formData
});
```

## 界面设计特点

### 1. 一致的设计风格
- 与现有页面保持一致的导航栏设计
- 统一的颜色方案和圆角设计
- 相同的按钮样式和交互效果

### 2. 用户体验优化
- 清晰的步骤指引
- 直观的图片预览和管理
- 丰富的设置选项
- 实时的进度反馈

### 3. 响应式布局
- 适配不同屏幕尺寸
- 合理的间距和布局
- 优雅的动画效果

## 文件结构
```
image-to-pdf/
├── image-to-pdf.wxml    # 页面结构
├── image-to-pdf.wxss    # 页面样式
├── image-to-pdf.js      # 页面逻辑
├── image-to-pdf.json    # 页面配置
└── README.md           # 说明文档
```

## 使用说明

1. 点击"选择图片"按钮选择要转换的图片
2. 调整图片顺序（可选）
3. 设置转换参数（页面适配、颜色类型等）
4. 点击"开始转换"按钮
5. 等待转换完成
6. 预览并下载生成的PDF文件

## 注意事项

1. 图片文件大小限制：建议单张图片不超过10MB
2. 图片数量限制：最多9张图片
3. 支持的图片格式：JPG、PNG、GIF、BMP、TIFF等
4. 转换时间取决于图片数量和大小
5. 需要网络连接来调用转换API
