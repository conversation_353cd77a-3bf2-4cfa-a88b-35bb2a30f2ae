// 服务器端API示例 - 调用Stirling-PDF的图片转PDF接口
// 这个文件展示了如何在Node.js服务器中集成Stirling-PDF API

const express = require('express');
const multer = require('multer');
const FormData = require('form-data');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const app = express();
const upload = multer({ dest: 'uploads/' });

// Stirling-PDF服务器配置
const STIRLING_PDF_BASE_URL = 'http://localhost:8080'; // 你的Stirling-PDF服务器地址
const STIRLING_PDF_API_KEY = 'your-api-key-here'; // 如果启用了认证

/**
 * 微信小程序调用的API接口
 * 接收图片文件和转换参数，调用Stirling-PDF进行转换
 */
app.post('/api/convert-images-to-pdf', upload.array('images', 9), async (req, res) => {
  try {
    const { fitOption, colorType, autoRotate } = req.body;
    const imageFiles = req.files;

    if (!imageFiles || imageFiles.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请上传至少一张图片'
      });
    }

    // 调用Stirling-PDF API
    const pdfResult = await convertImagesToPDF(imageFiles, {
      fitOption: fitOption || 'fitDocumentToImage',
      colorType: colorType || 'color',
      autoRotate: autoRotate === 'true'
    });

    // 返回结果给小程序
    res.json({
      success: true,
      fileName: pdfResult.fileName,
      fileSize: pdfResult.fileSize,
      previewUrl: pdfResult.previewUrl,
      downloadUrl: pdfResult.downloadUrl
    });

    // 清理临时文件
    imageFiles.forEach(file => {
      fs.unlinkSync(file.path);
    });

  } catch (error) {
    console.error('转换失败:', error);
    res.status(500).json({
      success: false,
      message: '转换失败: ' + error.message
    });
  }
});

/**
 * 调用Stirling-PDF API进行图片转PDF转换
 */
async function convertImagesToPDF(imageFiles, options) {
  const formData = new FormData();

  // 添加图片文件
  imageFiles.forEach((file, index) => {
    formData.append('fileInput', fs.createReadStream(file.path), {
      filename: file.originalname || `image${index + 1}.${getFileExtension(file.path)}`,
      contentType: file.mimetype || 'image/jpeg'
    });
  });

  // 添加转换参数
  formData.append('fitOption', options.fitOption);
  formData.append('colorType', options.colorType);
  formData.append('autoRotate', options.autoRotate);

  // 准备请求头
  const headers = {
    ...formData.getHeaders()
  };

  // 如果启用了认证，添加API密钥
  if (STIRLING_PDF_API_KEY) {
    headers['X-API-KEY'] = STIRLING_PDF_API_KEY;
  }

  try {
    // 调用Stirling-PDF API
    const response = await axios.post(
      `${STIRLING_PDF_BASE_URL}/api/v1/convert/img/pdf`,
      formData,
      {
        headers: headers,
        responseType: 'stream', // 接收PDF文件流
        timeout: 60000 // 60秒超时
      }
    );

    // 保存PDF文件
    const fileName = `converted_${Date.now()}.pdf`;
    const filePath = path.join('downloads', fileName);
    
    // 确保下载目录存在
    if (!fs.existsSync('downloads')) {
      fs.mkdirSync('downloads');
    }

    // 写入PDF文件
    const writer = fs.createWriteStream(filePath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        // 获取文件大小
        const stats = fs.statSync(filePath);
        const fileSize = formatFileSize(stats.size);

        resolve({
          fileName: fileName,
          fileSize: fileSize,
          filePath: filePath,
          previewUrl: `/api/preview/${fileName}`,
          downloadUrl: `/api/download/${fileName}`
        });
      });

      writer.on('error', reject);
    });

  } catch (error) {
    if (error.response) {
      // Stirling-PDF API返回的错误
      throw new Error(`Stirling-PDF API错误: ${error.response.status} - ${error.response.statusText}`);
    } else if (error.request) {
      // 网络错误
      throw new Error('无法连接到Stirling-PDF服务器');
    } else {
      // 其他错误
      throw new Error('转换过程中发生错误: ' + error.message);
    }
  }
}

/**
 * 提供PDF文件下载
 */
app.get('/api/download/:fileName', (req, res) => {
  const fileName = req.params.fileName;
  const filePath = path.join('downloads', fileName);

  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ error: '文件不存在' });
  }

  res.download(filePath, fileName, (err) => {
    if (err) {
      console.error('下载失败:', err);
      res.status(500).json({ error: '下载失败' });
    }
  });
});

/**
 * 提供PDF文件预览
 */
app.get('/api/preview/:fileName', (req, res) => {
  const fileName = req.params.fileName;
  const filePath = path.join('downloads', fileName);

  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ error: '文件不存在' });
  }

  res.setHeader('Content-Type', 'application/pdf');
  res.setHeader('Content-Disposition', 'inline; filename=' + fileName);
  
  const fileStream = fs.createReadStream(filePath);
  fileStream.pipe(res);
});

/**
 * 工具函数：获取文件扩展名
 */
function getFileExtension(filePath) {
  return path.extname(filePath).slice(1).toLowerCase();
}

/**
 * 工具函数：格式化文件大小
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 错误处理中间件
 */
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  res.status(500).json({
    success: false,
    message: '服务器内部错误'
  });
});

// 启动服务器
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
  console.log(`Stirling-PDF服务器地址: ${STIRLING_PDF_BASE_URL}`);
});

module.exports = app;
