<!--pages/pdf-split/pdf-split.wxml-->
<!-- 自定义导航栏 -->
<view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
  <view class="navbar-content">
    <view class="navbar-back" bindtap="onBackClick" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx;">
      <image class="navbar-back-icon" src="/images/back.svg"></image>
    </view>
    <view class="navbar-title" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx; line-height: {{menuButtonHeightRpx}}rpx;">PDF拆分</view>
  </view>
</view>

<!-- 拆分方式选择弹窗 -->
<view class="split-mode-modal" wx:if="{{showSplitModeModal}}" bindtap="hideSplitModeModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择拆分方式</text>
      <view class="modal-close" bindtap="hideSplitModeModal">
        <text class="close-icon">✕</text>
      </view>
    </view>

    <view class="modal-body">
      <view class="split-options">
        <view class="split-option {{splitMode === 'pages' ? 'active' : ''}}" bindtap="selectSplitMode" data-mode="pages">
          <view class="option-icon">
            <view class="icon-pages"></view>
          </view>
          <view class="option-info">
            <view class="option-name">按页数拆分</view>
            <view class="option-desc">指定每个文件包含的页数</view>
          </view>
        </view>

        <view class="split-option {{splitMode === 'range' ? 'active' : ''}}" bindtap="selectSplitMode" data-mode="range">
          <view class="option-icon">
            <view class="icon-range"></view>
          </view>
          <view class="option-info">
            <view class="option-name">按页码范围拆分</view>
            <view class="option-desc">指定具体的页码范围</view>
          </view>
        </view>

        <view class="split-option {{splitMode === 'single' ? 'active' : ''}}" bindtap="selectSplitMode" data-mode="single">
          <view class="option-icon">
            <view class="icon-single"></view>
          </view>
          <view class="option-info">
            <view class="option-name">拆分为单页</view>
            <view class="option-desc">每页生成一个独立文件</view>
          </view>
        </view>
      </view>
    </view>

    <view class="modal-footer">
      <view class="modal-btn cancel-btn" bindtap="hideSplitModeModal">
        <text>取消</text>
      </view>
      <view class="modal-btn confirm-btn" bindtap="confirmSplitMode">
        <text>确定</text>
      </view>
    </view>
  </view>
</view>

<!-- 页面内容 -->
<view class="container" style="padding-top: {{navbarHeight}}px;">

  <!-- 文件上传区域 -->
  <view class="upload-section" wx:if="{{!fileSelected}}">
    <view class="upload-area" bindtap="onSelectFile">
      <view class="upload-icon">
        <image class="upload-svg" src="/images/split-pdf.svg"></image>
      </view>
      <view class="upload-content">
        <view class="upload-title">选择PDF文件</view>
        <view class="upload-subtitle">点击选择需要拆分的PDF文件</view>
        <view class="upload-tips">支持PDF格式，最大50MB</view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content" wx:if="{{fileSelected && !splitting}}">
    <!-- 文件信息 -->
    <view class="file-info-card">
      <image class="file-icon" src="/images/pdf-icon.svg"></image>
      <view class="file-details">
        <view class="file-name">{{fileName}}</view>
        <view class="file-info">
          <text class="info-label">文件大小：</text>
          <text class="info-value">{{fileSize}}</text>
          <text class="info-separator">　</text>
          <text class="info-label">总页数：</text>
          <text class="info-value">{{totalPages}}页</text>
        </view>
      </view>
      <view class="remove-btn" bindtap="onRemoveFile">移除</view>
    </view>
  </view>

  <!-- 底部固定区域 -->
  <view class="footer-area" wx:if="{{fileSelected && !splitting && !splitted}}">
    <!-- 拆分设置 -->
    <view class="split-settings">
      <!-- 拆分方式选择 -->
      <view class="setting-item">
        <view class="setting-header">
          <text class="setting-label">拆分方式</text>
          <view class="setting-value-btn" bindtap="showSplitModeModal">
            <text class="setting-value">{{splitModeText}}</text>
            <text class="setting-arrow">></text>
          </view>
        </view>
      </view>

      <!-- 按页数拆分的设置 -->
      <view class="setting-item" wx:if="{{splitMode === 'pages'}}">
        <view class="setting-header">
          <text class="setting-label">每个文件页数</text>
          <text class="setting-value">{{pagesPerFile}}页</text>
        </view>
        <view class="slider-container">
          <slider
            value="{{pagesPerFile}}"
            min="1"
            max="{{totalPages}}"
            step="1"
            show-value="false"
            bindchange="onPagesPerFileChange"
            activeColor="#DC143C"
            backgroundColor="rgba(220, 20, 60, 0.1)"
            block-color="#ffffff"
            block-size="28"
          />
        </view>
        <view class="slider-tips">
          <text class="tip-left">1页</text>
          <text class="tip-right">{{totalPages}}页</text>
        </view>
      </view>

      <!-- 按页码范围拆分的设置 -->
      <view class="setting-item" wx:if="{{splitMode === 'range'}}">
        <view class="range-inputs">
          <view class="range-input-group">
            <text class="range-label">起始页</text>
            <input class="range-input" type="number" placeholder="1" value="{{rangeStart}}" bindinput="onRangeStartInput" />
          </view>
          <text class="range-separator">-</text>
          <view class="range-input-group">
            <text class="range-label">结束页</text>
            <input class="range-input" type="number" placeholder="{{totalPages}}" value="{{rangeEnd}}" bindinput="onRangeEndInput" />
          </view>
        </view>
        <view class="range-tip">页码范围：1 - {{totalPages}}</view>
      </view>

      <!-- 预估结果 -->
      <view class="estimate-section" wx:if="{{splitMode}}">
        <view class="estimate-header">
          <text class="estimate-title">预估结果</text>
        </view>
        <view class="estimate-info">
          <view class="estimate-item">
            <text class="estimate-label">将生成文件数</text>
            <text class="estimate-value">{{estimatedFiles}}个</text>
          </view>
          <view class="estimate-item" wx:if="{{splitMode === 'pages'}}">
            <text class="estimate-label">平均每个文件</text>
            <text class="estimate-value">{{pagesPerFile}}页</text>
          </view>
          <view class="estimate-item" wx:if="{{splitMode === 'range'}}">
            <text class="estimate-label">提取页数</text>
            <text class="estimate-value">{{rangePages}}页</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 拆分进行中 -->
  <view class="splitting-section" wx:if="{{splitting}}">
    <view class="progress-card">
      <view class="progress-icon">
        <image class="progress-svg rotating" src="/images/split-pdf.svg"></image>
      </view>
      <view class="progress-content">
        <view class="progress-title">正在拆分PDF...</view>
        <view class="progress-subtitle">请稍候，正在为您拆分文件</view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercent}}%"></view>
        </view>
        <view class="progress-text">{{progressPercent}}%</view>
      </view>
    </view>
  </view>

  <!-- 拆分完成 -->
  <view class="result-section" wx:if="{{splitted}}">
    <view class="result-card">
      <view class="result-icon">
        <image class="result-svg" src="/images/success.svg"></image>
      </view>
      <view class="result-content">
        <view class="result-title">拆分完成！</view>
        <view class="result-subtitle">成功生成 {{resultFiles.length}} 个文件</view>

        <!-- 文件列表 -->
        <view class="result-files">
          <view class="result-file" wx:for="{{resultFiles}}" wx:key="id">
            <image class="file-icon-small" src="/images/pdf-icon.svg"></image>
            <view class="file-info-small">
              <view class="file-name-small">{{item.name}}</view>
              <view class="file-pages-small">{{item.pages}}页</view>
            </view>
            <view class="download-btn-small" bindtap="onDownloadSingleFile" data-index="{{index}}">下载</view>
          </view>
        </view>

        <view class="result-actions">
          <view class="action-btn secondary" bindtap="onSplitAnother">
            <text>拆分其他文件</text>
          </view>
          <view class="action-btn primary" bindtap="onDownloadAll">
            <text>下载全部文件</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部胶囊按钮 -->
  <view class="bottom-capsule">
    <view class="capsule-btn left" bindtap="onSelectPDF">
      <view class="capsule-text">选择PDF</view>
    </view>
    <view class="capsule-btn right {{!canSplit ? 'disabled' : ''}}" bindtap="onStartSplit">
      <view class="capsule-text">开始拆分</view>
    </view>
  </view>

</view>