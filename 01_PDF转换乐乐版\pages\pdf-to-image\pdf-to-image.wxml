<!--pages/pdf-to-image/pdf-to-image.wxml-->
<!-- 自定义导航栏 -->
<view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
  <view class="navbar-content">
    <view class="navbar-back" bindtap="onBackClick" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx;">
      <image class="navbar-back-icon" src="/images/back.svg"></image>
    </view>
    <view class="navbar-title" style="top: {{menuButtonTopRpx}}rpx; height: {{menuButtonHeightRpx}}rpx; line-height: {{menuButtonHeightRpx}}rpx;">PDF转图片</view>
  </view>
</view>



<!-- 页面内容 -->
<view class="container" style="padding-top: {{navbarHeight}}px;">
  
  <!-- 文件上传区域 -->
  <view class="upload-section" wx:if="{{!fileSelected}}">
    <view class="upload-area" bindtap="onSelectFile">
      <view class="upload-icon">
        <image class="upload-svg" src="/images/pdf-to-image.svg"></image>
      </view>
      <view class="upload-content">
        <view class="upload-title">选择PDF文件</view>
        <view class="upload-subtitle">点击选择或拖拽文件到此处</view>
        <view class="upload-tips">支持PDF格式，将转换为图片</view>
      </view>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content" wx:if="{{fileSelected && !converting}}">
    <!-- 多文件显示 -->
    <view wx:if="{{selectedFiles.length > 1}}">
      <view class="file-info-card" wx:for="{{selectedFiles}}" wx:key="id">
        <image class="file-icon" src="/images/pdf-to-image.svg"></image>
        <view class="file-details">
          <view class="file-name">{{item.name}}</view>
          <view class="file-size-info">
            <text class="size-label">文件大小：</text>
            <text class="size-value">{{item.size}}</text>
          </view>
        </view>
        <view class="remove-btn" bindtap="onRemoveFile" data-id="{{item.id}}">移除</view>
      </view>
    </view>

    <!-- 单文件显示 -->
    <view wx:if="{{selectedFiles.length <= 1}}">
      <view class="file-info-card">
        <image class="file-icon" src="/images/pdf-to-image.svg"></image>
        <view class="file-details">
          <view class="file-name">{{fileName}}</view>
          <view class="file-size-info">
            <text class="size-label">文件大小：</text>
            <text class="size-value">{{fileSize}}</text>
          </view>
        </view>
        <view class="remove-btn" bindtap="onSettings">设置</view>
      </view>
    </view>
  </view>

  <!-- 底部固定区域 -->
  <view class="footer-area" wx:if="{{fileSelected && !converting && !converted}}">
  <!-- 页面范围设置面板 -->
  <view class="page-range-panel">
    <view class="panel-header">
      <text class="panel-label">转换页面</text>
      <text class="panel-value">{{pageRangeText}}</text>
    </view>
    <view class="range-options">
      <view class="range-item {{pageRange === 'all' ? 'active' : ''}}" bindtap="onPageRangeChange" data-range="all">
        <text class="range-text">全部页面</text>
      </view>
      <view class="range-item {{pageRange === 'custom' ? 'active' : ''}}" bindtap="onPageRangeChange" data-range="custom">
        <text class="range-text">自定义</text>
      </view>
    </view>
    <view class="custom-range-input" wx:if="{{pageRange === 'custom'}}">
      <input
        class="range-input"
        placeholder="例如：1-5, 8, 10-12"
        value="{{customPageRange}}"
        bindinput="onCustomPageRangeInput"
      />
    </view>
  </view>
  </view>

  <!-- 转换进行中 -->
  <view class="converting-section" wx:if="{{converting}}">
    <view class="progress-card">
      <view class="progress-icon">
        <image class="progress-svg" src="/images/pdf-to-image.svg"></image>
      </view>
      <view class="progress-content">
        <view class="progress-title">正在转换为图片...</view>
        <view class="progress-subtitle">请稍候，正在处理PDF文件</view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{progressPercent}}%"></view>
        </view>
        <view class="progress-text">{{progressPercent}}%</view>
      </view>
    </view>
  </view>

  <!-- 转换完成 -->
  <view class="result-section" wx:if="{{converted}}">
    <view class="result-card">
      <view class="result-icon">
        <image class="result-svg" src="/images/success.svg"></image>
      </view>
      <view class="result-content">
        <view class="result-title">转换完成！</view>
        <view class="result-subtitle">成功转换 {{convertedImageCount}} 张图片</view>

        <!-- 图片预览 -->
        <view class="image-preview" wx:if="{{convertedImages.length > 0}}">
          <scroll-view class="preview-scroll" scroll-x="true">
            <view class="preview-item" wx:for="{{convertedImages}}" wx:key="index">
              <image class="preview-image" src="{{item.url}}" mode="aspectFit" bindtap="previewImage" data-index="{{index}}"></image>
              <text class="preview-name">第{{index + 1}}页</text>
            </view>
          </scroll-view>
        </view>

        <view class="result-actions">
          <view class="action-btn secondary" bindtap="onConvertAnother">
            <text>转换其他文件</text>
          </view>
          <view class="action-btn primary" bindtap="onDownloadImages">
            <text>下载图片</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部胶囊按钮 -->
  <view class="bottom-capsule" wx:if="{{!converting}}">
    <view class="capsule-btn left" bindtap="onSelectPDF">
      <view class="capsule-text">选择PDF</view>
    </view>
    <view class="capsule-btn right {{!fileSelected ? 'disabled' : ''}}" bindtap="onStartConversion" wx:if="{{!converted}}">
      <view class="capsule-text">开始转换</view>
    </view>
    <view class="capsule-btn right" bindtap="onDownloadImages" wx:if="{{converted}}">
      <view class="capsule-text">下载图片</view>
    </view>
  </view>

</view>
